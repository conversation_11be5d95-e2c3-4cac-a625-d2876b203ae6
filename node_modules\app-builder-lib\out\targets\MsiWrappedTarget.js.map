{"version": 3, "file": "MsiWrappedTarget.js", "sourceRoot": "", "sources": ["../../src/targets/MsiWrappedTarget.ts"], "names": [], "mappings": ";;AAAA,+CAA+C;AAC/C,+DAA2C;AAC3C,6BAA4B;AAK5B,2CAAmC;AAEnC,MAAM,4BAA4B,GAAG,2BAAI,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAA;AAEvF,MAAqB,gBAAiB,SAAQ,mBAAS;IAMrD,YACE,QAAqB,EACZ,MAAc;QAEvB,+CAA+C;QAC/C,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,KAAK,CAAC,CAAA;QAHnC,WAAM,GAAN,MAAM,CAAQ;QAPhB,YAAO,GAAsB,IAAA,yBAAU,EAAC,IAAI,CAAC,QAAQ,CAAC,4BAA4B,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;QAE7H,eAAe;QACE,UAAK,GAAsB,IAAI,GAAG,EAAE,CAAA;IAQrD,CAAC;IAED,IAAY,SAAS;QACnB,kDAAkD;QAClD,mDAAmD;QACnD,uBAAuB;QACvB,OAAO,2BAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,4BAA4B,CAAC,CAAC,WAAW,EAAE,CAAA;IACtF,CAAC;IAEO,qBAAqB;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;QACnC,0DAA0D;QAC1D,sEAAsE;QAEtE,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3E,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAC7C,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAA;QAChC,MAAM,UAAU,GAAG,MAAM,CAAA;QACzB,IACE,CAAC,MAAM;aACJ,GAAG,CAAC,CAAC,CAA+B,EAAU,EAAE;YAC/C,MAAM,MAAM,GAAW,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;YAC3D,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAA;QACpC,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,UAAU,CAAC,EAC9B,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAA;QACxE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAiB,EAAE,IAAU;QACjC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QAC/B,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAED,WAAW;QACT,4DAA4D;QAC5D,mDAAmD;QACnD,oCAAoC;QACpC,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK,CAAA;QAE3D,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAE5B,OAAO,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;IACrC,CAAC;IAED,IAAc,wBAAwB;QACpC,4EAA4E;QAC5E,6CAA6C;QAC7C,OAAO,wCAAwC,CAAA;IACjD,CAAC;IAEO,gBAAgB,CAAC,IAAU;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,qFAAqF;QACrF,MAAM,YAAY,GAAG,QAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,wBAAwB,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAA;QAChL,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QAEzD,OAAO,YAAY,CAAA;IACrB,CAAC;IAES,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,IAAU,EAAE,aAAiD;QAC7G,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;QACjD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,OAAO,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACxC,GAAG,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC7C,aAAa,EAAE,aAAa;YAC5B,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,WAAW,EAAE,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;YACxD,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;SACnD,CAAC,CAAA;IACJ,CAAC;CACF;AAvFD,mCAuFC", "sourcesContent": ["import { Arch, deepAssign } from \"builder-util\"\nimport { UUID } from \"builder-util-runtime\"\nimport * as path from \"path\"\nimport { MsiWrappedOptions } from \"../\"\nimport { TargetConfiguration } from \"../core\"\nimport { FinalCommonWindowsInstallerOptions } from \"../options/CommonWindowsInstallerConfiguration\"\nimport { WinPackager } from \"../winPackager\"\nimport MsiTarget from \"./MsiTarget\"\n\nconst ELECTRON_MSI_WRAPPED_NS_UUID = UUID.parse(\"467f7bb2-a83c-442f-b776-394d316e8e53\")\n\nexport default class MsiWrappedTarget extends MsiTarget {\n  readonly options: MsiWrappedOptions = deepAssign(this.packager.platformSpecificBuildOptions, this.packager.config.msiWrapped)\n\n  /** @private */\n  private readonly archs: Map<Arch, string> = new Map()\n\n  constructor(\n    packager: WinPackager,\n    readonly outDir: string\n  ) {\n    // must be synchronous so it can run after nsis\n    super(packager, outDir, \"msiWrapped\", false)\n  }\n\n  private get productId(): string {\n    // this id is only required to build the installer\n    // however it serves no purpose as this msi is just\n    // a wrapper for an exe\n    return UUID.v5(this.packager.appInfo.id, ELECTRON_MSI_WRAPPED_NS_UUID).toUpperCase()\n  }\n\n  private validatePrerequisites(): void {\n    const config = this.packager.config\n    // this target requires nsis to be configured and executed\n    // as this build re-bundles the nsis executable and wraps it in an msi\n\n    if (!config.win || !config.win.target || !Array.isArray(config.win.target)) {\n      throw new Error(\"No windows target found!\")\n    }\n\n    const target = config.win.target\n    const nsisTarget = \"nsis\"\n    if (\n      !target\n        .map((t: TargetConfiguration | string): string => {\n          const result: string = typeof t === \"string\" ? t : t.target\n          return result.toLowerCase().trim()\n        })\n        .some(t => t === nsisTarget)\n    ) {\n      throw new Error(\"No nsis target found! Please specify an nsis target\")\n    }\n  }\n\n  build(appOutDir: string, arch: Arch): Promise<any> {\n    this.archs.set(arch, appOutDir)\n    return Promise.resolve()\n  }\n\n  finishBuild(): Promise<any> {\n    // this target invokes `build` in `finishBuild` to guarantee\n    // that the dependent target has already been built\n    // this also affords us re-usability\n    const [arch, appOutDir] = this.archs.entries().next().value\n\n    this.validatePrerequisites()\n\n    return super.build(appOutDir, arch)\n  }\n\n  protected get installerFilenamePattern(): string {\n    // big assumption is made here for the moment that the pattern didn't change\n    // tslint:disable:no-invalid-template-strings\n    return \"${productName} Setup ${version}.${ext}\"\n  }\n\n  private getExeSourcePath(arch: Arch) {\n    const packager = this.packager\n    // in this case, we want .exe, this way we can wrap the existing package if it exists\n    const artifactName = packager.expandArtifactNamePattern(this.options, \"exe\", arch, this.installerFilenamePattern, false, this.packager.platformSpecificBuildOptions.defaultArch)\n    const artifactPath = path.join(this.outDir, artifactName)\n\n    return artifactPath\n  }\n\n  protected async writeManifest(_appOutDir: string, arch: Arch, commonOptions: FinalCommonWindowsInstallerOptions) {\n    const exeSourcePath = this.getExeSourcePath(arch)\n    const options = this.options\n\n    return (await this.projectTemplate.value)({\n      ...(await this.getBaseOptions(commonOptions)),\n      exeSourcePath: exeSourcePath,\n      productId: this.productId,\n      impersonate: options.impersonate === true ? \"yes\" : \"no\",\n      wrappedInstallerArgs: options.wrappedInstallerArgs,\n    })\n  }\n}\n"]}