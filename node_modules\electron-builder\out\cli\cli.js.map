{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../../src/cli/cli.ts"], "names": [], "mappings": ";;;AAEA,+CAA6D;AAC7D,+BAA8B;AAC9B,uCAAmC;AACnC,8BAA6B;AAC7B,6BAA4B;AAC5B,uDAA0C;AAC1C,gDAAiD;AACjD,wCAAsE;AACtE,uEAAgE;AAChE,yDAAmF;AACnF,mCAA+B;AAC/B,wDAA8D;AAC9D,kFAAiF;AAEjF,sCAAsC;AACtC,KAAK,IAAA,qBAAW,GAAE;KACf,OAAO,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE,OAAO,EAAE,+BAAqB,EAAE,IAAI,CAAC,eAAK,CAAC,CAAC;KACpE,OAAO,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,iDAA8B,EAAE,IAAI,CAAC,iCAAc,CAAC,CAAC;KACrG,OAAO,CAAC,kBAAkB,EAAE,yBAAyB,EAAE,iDAA8B,CAAC,gDAAgD,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;KACnK,OAAO,CACN,yBAAyB,EACzB,uDAAuD,EACvD,KAAK,CAAC,EAAE,CACN,KAAK;KACF,MAAM,CAAC,WAAW,EAAE;IACnB,KAAK,EAAE,CAAC,GAAG,CAAC;IACZ,IAAI,EAAE,QAAQ;IACd,WAAW,EAAE,IAAI;IACjB,WAAW,EAAE,oBAAoB;CAClC,CAAC;KACD,YAAY,CAAC,WAAW,CAAC,EAC9B,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAA,8CAAoB,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CACnD;KACA,OAAO,CACN,OAAO,EACP,8DAA8D,EAC9D,KAAK,CAAC,EAAE,CAAC,KAAK,EACd,IAAI,CAAC,GAAG,EAAE,CAAC,IAAA,aAAK,GAAE,CAAC,CACpB;KACA,IAAI,EAAE;KACN,MAAM,CAAC,OAAO,KAAK,CAAC,SAAS,CAAC,wBAAwB,CAAC,0BAA0B,CAAC;KAClF,MAAM,EAAE;KACR,iBAAiB,EAAE,CAAC,IAAI,CAAA;AAE3B,SAAS,IAAI,CAAC,IAAiC;IAC7C,OAAO,CAAC,IAAS,EAAE,EAAE;QACnB,eAAe,EAAE,CAAC,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,kBAAG,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,sBAAsB,CAAC,CAAC,CAAA;QACnF,IAAA,0BAAO,EAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,sBAAsB,CAAC,CAAC;aACtD,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACtB,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAA;YACpB,oEAAoE;YACpE,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAA;YAChD,IAAI,KAAK,YAAY,wCAAyB,EAAE,CAAC;gBAC/C,kBAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;YAChC,CAAC;iBAAM,IAAI,CAAC,CAAC,KAAK,YAAY,gBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;gBACjE,kBAAG,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;YAC9E,CAAC;QACH,CAAC,CAAC,CAAA;IACN,CAAC,CAAA;AACH,CAAC;AAED,KAAK,UAAU,eAAe;IAC5B,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,IAAI,EAAE,CAAC;QACnD,OAAM;IACR,CAAC;IAED,MAAM,GAAG,GAAG,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC,CAAA;IAC5E,IAAI,GAAG,CAAC,OAAO,KAAK,wBAAwB,EAAE,CAAC;QAC7C,OAAM;IACR,CAAC;IACD,MAAM,cAAc,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAA;IACxD,MAAM,cAAc,CAAC,EAAE,GAAG,EAAE,CAAC,CAAA;AAC/B,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAC,IAAS;IAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;IAChC,6CAA6C;IAC7C,OAAO,IAAA,qBAAc,EAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,MAAM,IAAA,oCAAkB,EAAC,UAAU,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAA;AACzH,CAAC", "sourcesContent": ["#! /usr/bin/env node\n\nimport { InvalidConfigurationError, log } from \"builder-util\"\nimport * as chalk from \"chalk\"\nimport { readJson } from \"fs-extra\"\nimport * as isCi from \"is-ci\"\nimport * as path from \"path\"\nimport { loadEnv } from \"read-config-file\"\nimport { ExecError } from \"builder-util/out/util\"\nimport { build, configureBuildCommand, createYargs } from \"../builder\"\nimport { createSelfSignedCert } from \"./create-self-signed-cert\"\nimport { configureInstallAppDepsCommand, installAppDeps } from \"./install-app-deps\"\nimport { start } from \"./start\"\nimport { nodeGypRebuild } from \"app-builder-lib/out/util/yarn\"\nimport { getElectronVersion } from \"app-builder-lib/out/electron/electronVersion\"\n\n// tslint:disable:no-unused-expression\nvoid createYargs()\n  .command([\"build\", \"*\"], \"Build\", configureBuildCommand, wrap(build))\n  .command(\"install-app-deps\", \"Install app deps\", configureInstallAppDepsCommand, wrap(installAppDeps))\n  .command(\"node-gyp-rebuild\", \"Rebuild own native code\", configureInstallAppDepsCommand /* yes, args the same as for install app deps */, wrap(rebuildAppNativeCode))\n  .command(\n    \"create-self-signed-cert\",\n    \"Create self-signed code signing cert for Windows apps\",\n    yargs =>\n      yargs\n        .option(\"publisher\", {\n          alias: [\"p\"],\n          type: \"string\",\n          requiresArg: true,\n          description: \"The publisher name\",\n        })\n        .demandOption(\"publisher\"),\n    wrap(argv => createSelfSignedCert(argv.publisher))\n  )\n  .command(\n    \"start\",\n    \"Run application in a development mode using electron-webpack\",\n    yargs => yargs,\n    wrap(() => start())\n  )\n  .help()\n  .epilog(`See ${chalk.underline(\"https://electron.build\")} for more documentation.`)\n  .strict()\n  .recommendCommands().argv\n\nfunction wrap(task: (args: any) => Promise<any>) {\n  return (args: any) => {\n    checkIsOutdated().catch((e: any) => log.warn({ error: e }, \"cannot check updates\"))\n    loadEnv(path.join(process.cwd(), \"electron-builder.env\"))\n      .then(() => task(args))\n      .catch(error => {\n        process.exitCode = 1\n        // https://github.com/electron-userland/electron-builder/issues/2940\n        process.on(\"exit\", () => (process.exitCode = 1))\n        if (error instanceof InvalidConfigurationError) {\n          log.error(null, error.message)\n        } else if (!(error instanceof ExecError) || !error.alreadyLogged) {\n          log.error({ failedTask: task.name, stackTrace: error.stack }, error.message)\n        }\n      })\n  }\n}\n\nasync function checkIsOutdated() {\n  if (isCi || process.env.NO_UPDATE_NOTIFIER != null) {\n    return\n  }\n\n  const pkg = await readJson(path.join(__dirname, \"..\", \"..\", \"package.json\"))\n  if (pkg.version === \"0.0.0-semantic-release\") {\n    return\n  }\n  const UpdateNotifier = require(\"simple-update-notifier\")\n  await UpdateNotifier({ pkg })\n}\n\nasync function rebuildAppNativeCode(args: any) {\n  const projectDir = process.cwd()\n  // this script must be used only for electron\n  return nodeGypRebuild(args.platform, args.arch, { version: await getElectronVersion(projectDir), useCustomDist: true })\n}\n"]}