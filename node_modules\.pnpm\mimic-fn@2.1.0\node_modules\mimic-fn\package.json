{"name": "mimic-fn", "version": "2.1.0", "description": "Make a function mimic another one", "license": "MIT", "repository": "sindresorhus/mimic-fn", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["function", "mimic", "imitate", "rename", "copy", "inherit", "properties", "name", "func", "fn", "set", "infer", "change"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}}