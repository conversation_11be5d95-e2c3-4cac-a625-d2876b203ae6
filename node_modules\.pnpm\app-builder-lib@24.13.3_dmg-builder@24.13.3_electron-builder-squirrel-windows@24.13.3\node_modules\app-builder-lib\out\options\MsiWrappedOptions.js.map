{"version": 3, "file": "MsiWrappedOptions.js", "sourceRoot": "", "sources": ["../../src/options/MsiWrappedOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TargetSpecificOptions } from \"../core\"\nimport { CommonWindowsInstallerConfiguration } from \"./CommonWindowsInstallerConfiguration\"\n\nexport interface MsiWrappedOptions extends CommonWindowsInstallerConfiguration, TargetSpecificOptions {\n  /**\n   * Extra arguments to provide to the wrapped installer (ie: /S for silent install)\n   */\n  readonly wrappedInstallerArgs?: string | null\n\n  /**\n   * Determines if the wrapped installer should be executed with impersonation\n   * @default false\n   */\n  readonly impersonate?: boolean\n\n  /**\n   * The [upgrade code](https://msdn.microsoft.com/en-us/library/windows/desktop/aa372375(v=vs.85).aspx). Optional, by default generated using app id.\n   */\n  readonly upgradeCode?: string | null\n\n  /**\n   * If `warningsAsErrors` is `true` (default): treat warnings as errors. If `warningsAsErrors` is `false`: allow warnings.\n   * @default true\n   */\n  readonly warningsAsErrors?: boolean\n\n  /**\n   * Any additional arguments to be passed to the WiX installer compiler, such as `[\"-ext\", \"WixUtilExtension\"]`\n   */\n  readonly additionalWixArgs?: Array<string> | null\n}\n"]}