<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP Camera Circle</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body style="-webkit-app-region: drag">
    <!-- Main circular container -->
    <div id="circle-container">
        <!-- Settings overlay (hidden by default) -->
        <div id="settings-overlay" class="hidden">
            <div class="settings-content">
                <h3>Camera Settings</h3>
                
                <!-- Camera Address -->
                <div class="setting-group">
                    <label>IP Address:</label>
                    <input type="text" id="ip-addr" placeholder="************" value="************">
                </div>
                
                <div class="setting-group">
                    <label>Port:</label>
                    <input type="text" id="port" placeholder="8080" value="8080">
                </div>
                
                <div class="setting-group">
                    <button id="connect-btn" class="btn primary">Connect</button>
                </div>
                
                <!-- Camera Controls -->
                <div class="setting-group">
                    <label>Camera:</label>
                    <div class="radio-group">
                        <label><input type="radio" name="camera" value="front" checked> Front</label>
                        <label><input type="radio" name="camera" value="rear"> Rear</label>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label>Flash:</label>
                    <div class="radio-group">
                        <label><input type="radio" name="flash" value="on"> On</label>
                        <label><input type="radio" name="flash" value="off" checked> Off</label>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label>Night Vision:</label>
                    <div class="radio-group">
                        <label><input type="radio" name="nightvision" value="on"> On</label>
                        <label><input type="radio" name="nightvision" value="off" checked> Off</label>
                    </div>
                </div>
                
                <div class="setting-group">
                    <label>NV Gain: <span id="nv-gain-value">1</span></label>
                    <input type="range" id="nv-gain" min="1" max="240" value="1">
                </div>
                
                <div class="setting-group">
                    <label>NV Exposure: <span id="nv-exposure-value">1</span></label>
                    <input type="range" id="nv-exposure" min="1" max="20" value="1">
                </div>
                
                <div class="setting-group">
                    <button id="take-snapshot" class="btn secondary">Take Screenshot</button>
                </div>
                
                <div class="setting-group">
                    <button id="close-settings" class="btn secondary">Close Settings</button>
                </div>
            </div>
        </div>
        
        <!-- Video feed container -->
        <div id="video-container">
            <img id="video-feed" src="" alt="Camera Feed">
            <div id="connection-status" class="status-overlay">
                <div class="status-text">Not Connected</div>
                <div class="status-subtitle">Right-click to configure</div>
            </div>
        </div>
        
        <!-- Control buttons overlay -->
        <div id="controls-overlay" class="hidden">
            <div class="control-btn" id="settings-btn" title="Settings">⚙️</div>
            <div class="control-btn" id="minimize-btn" title="Minimize">➖</div>
            <div class="control-btn" id="close-btn" title="Close">✖️</div>
        </div>
        
        <!-- Resize handle -->
        <div id="resize-handle" style="-webkit-app-region: no-drag"></div>
    </div>
    
    <!-- Context menu (will be shown on right-click) -->
    <div id="context-menu" class="context-menu hidden">
        <div class="menu-item" id="menu-settings">Settings</div>
        <div class="menu-item" id="menu-snapshot">Take Screenshot</div>
        <div class="menu-separator"></div>
        <div class="menu-item" id="menu-minimize">Minimize</div>
        <div class="menu-item" id="menu-close">Close</div>
    </div>

    <script src="renderer.js"></script>
</body>
</html>
