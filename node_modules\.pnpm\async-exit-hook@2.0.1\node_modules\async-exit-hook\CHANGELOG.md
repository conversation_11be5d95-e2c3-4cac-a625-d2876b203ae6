# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

<a name="2.0.1"></a>
## [2.0.1](https://github.com/tapppi/async-exit-hook/compare/v2.0.0...v2.0.1) (2017-08-03)



<a name="2.0.0"></a>
# [2.0.0](https://github.com/tapppi/async-exit-hook/compare/v1.1.2...v2.0.0) (2017-08-03)


### Features

* add unhandledRejectionHandler ([#3](https://github.com/tapppi/async-exit-hook/issues/3)) ([96a194f](https://github.com/tapppi/async-exit-hook/commit/96a194f))


### BREAKING CHANGES

* unhandledExceptionHandler no longer
catches rejections.



<a name="1.1.2"></a>
## [1.1.2](https://github.com/tapppi/async-exit-hook/compare/v1.1.1...v1.1.2) (2017-03-29)


### Bug Fixes

* filters are used individually for events [#1](https://github.com/tapppi/async-exit-hook/issues/1) ([03235c8](https://github.com/tapppi/async-exit-hook/commit/03235c8))



<a name="1.1.1"></a>
## [1.1.1](https://github.com/tapppi/async-exit-hook/compare/v1.1.0...v1.1.1) (2016-11-04)


### Bug Fixes

* unhandled rejections now handled ([4302b9e](https://github.com/tapppi/async-exit-hook/commit/4302b9e))


### Chores

* drop support for node 0.12 ([2830391](https://github.com/tapppi/async-exit-hook/commit/2830391))


### BREAKING CHANGES

* node 0.12 not tested anymore



<a name="1.1.0"></a>
# [1.1.0](https://github.com/tapppi/async-exit-hook/compare/v1.0.0...v1.1.0) (2016-10-13)


### Features

* support uncaughtRejectionHandler ([9098e3c](https://github.com/tapppi/async-exit-hook/commit/9098e3c))
