{"version": 3, "file": "configuration.js", "sourceRoot": "", "sources": ["../src/configuration.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Arch } from \"builder-util\"\nimport { BeforeBuildContext, Target } from \"./core\"\nimport { ElectronBrandingOptions, ElectronDownloadOptions } from \"./electron/ElectronFramework\"\nimport { PrepareApplicationStageDirectoryOptions } from \"./Framework\"\nimport { AppXOptions } from \"./options/AppXOptions\"\nimport { AppImageOptions, DebOptions, FlatpakOptions, LinuxConfiguration, LinuxTargetSpecificOptions } from \"./options/linuxOptions\"\nimport { DmgOptions, MacConfiguration, MasConfiguration } from \"./options/macOptions\"\nimport { MsiOptions } from \"./options/MsiOptions\"\nimport { MsiWrappedOptions } from \"./options/MsiWrappedOptions\"\nimport { PkgOptions } from \"./options/pkgOptions\"\nimport { PlatformSpecificBuildOptions } from \"./options/PlatformSpecificBuildOptions\"\nimport { SnapOptions } from \"./options/SnapOptions\"\nimport { SquirrelWindowsOptions } from \"./options/SquirrelWindowsOptions\"\nimport { WindowsConfiguration } from \"./options/winOptions\"\nimport { BuildResult } from \"./packager\"\nimport { ArtifactBuildStarted, ArtifactCreated } from \"./packagerApi\"\nimport { PlatformPackager } from \"./platformPackager\"\nimport { NsisOptions, NsisWebOptions, PortableOptions } from \"./targets/nsis/nsisOptions\"\n\n// duplicate appId here because it is important\n/**\n * Configuration Options\n */\nexport interface Configuration extends PlatformSpecificBuildOptions {\n  /**\n   * The application id. Used as [CFBundleIdentifier](https://developer.apple.com/library/ios/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html#//apple_ref/doc/uid/20001431-102070) for MacOS and as\n   * [Application User Model ID](https://msdn.microsoft.com/en-us/library/windows/desktop/dd378459(v=vs.85).aspx) for Windows (NSIS target only, Squirrel.Windows not supported). It is strongly recommended that an explicit ID is set.\n   * @default com.electron.${name}\n   */\n  readonly appId?: string | null\n\n  /**\n   * As [name](#Metadata-name), but allows you to specify a product name for your executable which contains spaces and other special characters not allowed in the [name property](https://docs.npmjs.com/files/package.json#name).\n   * If not specified inside of the `build` configuration, `productName` property defined at the top level of `package.json` is used. If not specified at the top level of `package.json`, [name property](https://docs.npmjs.com/files/package.json#name) is used.\n   */\n  readonly productName?: string | null\n\n  /**\n   * The human-readable copyright line for the app.\n   * @default Copyright © year ${author}\n   */\n  readonly copyright?: string | null\n\n  readonly directories?: MetadataDirectories | null\n\n  /**\n   * Options related to how build macOS targets.\n   */\n  readonly mac?: MacConfiguration | null\n  /**\n   * MAS (Mac Application Store) options.\n   */\n  readonly mas?: MasConfiguration | null\n  /**\n   * MAS (Mac Application Store) development options (`mas-dev` target).\n   */\n  readonly masDev?: MasConfiguration | null\n  /**\n   * macOS DMG options.\n   */\n  readonly dmg?: DmgOptions | null\n  /**\n   * macOS PKG options.\n   */\n  readonly pkg?: PkgOptions | null\n\n  /**\n   * Options related to how build Windows targets.\n   */\n  readonly win?: WindowsConfiguration | null\n  readonly nsis?: NsisOptions | null\n  readonly nsisWeb?: NsisWebOptions | null\n  readonly portable?: PortableOptions | null\n  readonly appx?: AppXOptions | null\n  /** @private */\n  readonly msi?: MsiOptions | null\n  /** @private */\n  readonly msiWrapped?: MsiWrappedOptions | null\n  readonly squirrelWindows?: SquirrelWindowsOptions | null\n\n  /**\n   * Options related to how build Linux targets.\n   */\n  readonly linux?: LinuxConfiguration | null\n  /**\n   * Debian package options.\n   */\n  readonly deb?: DebOptions | null\n  /**\n   * Snap options.\n   */\n  readonly snap?: SnapOptions | null\n  /**\n   * AppImage options.\n   */\n  readonly appImage?: AppImageOptions | null\n  /**\n   * Flatpak options.\n   */\n  readonly flatpak?: FlatpakOptions | null\n  readonly pacman?: LinuxTargetSpecificOptions | null\n  readonly rpm?: LinuxTargetSpecificOptions | null\n  readonly freebsd?: LinuxTargetSpecificOptions | null\n  readonly p5p?: LinuxTargetSpecificOptions | null\n  readonly apk?: LinuxTargetSpecificOptions | null\n\n  /**\n   * Whether to include *all* of the submodules node_modules directories\n   * @default false\n   */\n  includeSubNodeModules?: boolean\n\n  /**\n   * Whether to build the application native dependencies from source.\n   * @default false\n   */\n  buildDependenciesFromSource?: boolean\n  /**\n   * Whether to execute `node-gyp rebuild` before starting to package the app.\n   *\n   * Don't [use](https://github.com/electron-userland/electron-builder/issues/683#issuecomment-241214075) [npm](http://electron.atom.io/docs/tutorial/using-native-node-modules/#using-npm) (neither `.npmrc`) for configuring electron headers. Use `electron-builder node-gyp-rebuild` instead.\n   * @default false\n   */\n  readonly nodeGypRebuild?: boolean\n  /**\n   * Additional command line arguments to use when installing app native deps.\n   */\n  readonly npmArgs?: Array<string> | string | null\n  /**\n   * Whether to [rebuild](https://docs.npmjs.com/cli/rebuild) native dependencies before starting to package the app.\n   * @default true\n   */\n  readonly npmRebuild?: boolean\n\n  /**\n   * The build number. Maps to the `--iteration` flag for builds using FPM on Linux.\n   * If not defined, then it will fallback to `BUILD_NUMBER` or `TRAVIS_BUILD_NUMBER` or `APPVEYOR_BUILD_NUMBER` or `CIRCLE_BUILD_NUM` or `BUILD_BUILDNUMBER` or `CI_PIPELINE_IID` env.\n   */\n  readonly buildNumber?: string | null\n\n  /**\n   * The build version. Maps to the `CFBundleVersion` on macOS, and `FileVersion` metadata property on Windows. Defaults to the `version`.\n   * If `buildVersion` is not defined and `buildNumber` (or one of the `buildNumber` envs) is defined, it will be used as a build version (`version.buildNumber`).\n   */\n  readonly buildVersion?: string | null\n\n  /**\n   * Whether to download the alternate FFmpeg library from Electron's release assets and replace the default FFmpeg library prior to signing\n   */\n  readonly downloadAlternateFFmpeg?: boolean\n\n  /**\n   * Whether to use [electron-compile](http://github.com/electron/electron-compile) to compile app. Defaults to `true` if `electron-compile` in the dependencies. And `false` if in the `devDependencies` or doesn't specified.\n   */\n  readonly electronCompile?: boolean\n\n  /**\n   * Returns the path to custom Electron build (e.g. `~/electron/out/R`). Zip files must follow the pattern `electron-v${version}-${platformName}-${arch}.zip`, otherwise it will be assumed to be an unpacked Electron app directory\n   */\n  readonly electronDist?: string | ((options: PrepareApplicationStageDirectoryOptions) => string)\n\n  /**\n   * The [electron-download](https://github.com/electron-userland/electron-download#usage) options.\n   */\n  readonly electronDownload?: ElectronDownloadOptions\n\n  /**\n   * The branding used by Electron's distributables. This is needed if a fork has modified Electron's BRANDING.json file.\n   */\n  readonly electronBranding?: ElectronBrandingOptions\n\n  /**\n   * The version of electron you are packaging for. Defaults to version of `electron`, `electron-prebuilt` or `electron-prebuilt-compile` dependency.\n   */\n  electronVersion?: string | null\n\n  /**\n   * The name of a built-in configuration preset (currently, only `react-cra` is supported) or any number of paths to config files (relative to project dir).\n   *\n   * The latter allows to mixin a config from multiple other configs, as if you `Object.assign` them, but properly combine `files` glob patterns.\n   *\n   * If `react-scripts` in the app dependencies, `react-cra` will be set automatically. Set to `null` to disable automatic detection.\n   */\n  extends?: Array<string> | string | null\n\n  /**\n   * Inject properties to `package.json`.\n   */\n  readonly extraMetadata?: any\n\n  /**\n   * Whether to fail if the application is not signed (to prevent unsigned app if code signing configuration is not correct).\n   * @default false\n   */\n  readonly forceCodeSigning?: boolean\n\n  /**\n   * *libui-based frameworks only* The version of NodeJS you are packaging for.\n   * You can set it to `current` to set the Node.js version that you use to run.\n   */\n  readonly nodeVersion?: string | null\n\n  /**\n   * *libui-based frameworks only* The version of LaunchUI you are packaging for. Applicable for Windows only. Defaults to version suitable for used framework version.\n   */\n  readonly launchUiVersion?: boolean | string | null\n\n  /**\n   * The framework name. One of `electron`, `proton`, `libui`. Defaults to `electron`.\n   */\n  readonly framework?: string | null\n\n  /**\n   * The function (or path to file or module id) to be [run before pack](#beforepack)\n   */\n  readonly beforePack?: ((context: BeforePackContext) => Promise<any> | any) | string | null\n\n  /**\n   * The function (or path to file or module id) to be [run after pack](#afterpack) (but before pack into distributable format and sign).\n   */\n  readonly afterPack?: ((context: AfterPackContext) => Promise<any> | any) | string | null\n  /**\n   * The function (or path to file or module id) to be [run after pack and sign](#aftersign) (but before pack into distributable format).\n   */\n  readonly afterSign?: ((context: AfterPackContext) => Promise<any> | any) | string | null\n\n  /**\n   * The function (or path to file or module id) to be run on artifact build start.\n   */\n  readonly artifactBuildStarted?: ((context: ArtifactBuildStarted) => Promise<any> | any) | string | null\n  /**\n   * The function (or path to file or module id) to be run on artifact build completed.\n   */\n  readonly artifactBuildCompleted?: ((context: ArtifactCreated) => Promise<any> | any) | string | null\n  /**\n   * The function (or path to file or module id) to be [run after all artifacts are build](#afterAllArtifactBuild).\n   */\n  readonly afterAllArtifactBuild?: ((context: BuildResult) => Promise<Array<string>> | Array<string>) | string | null\n  /**\n   * MSI project created on disk - not packed into .msi package yet.\n   */\n  readonly msiProjectCreated?: ((path: string) => Promise<any> | any) | string | null\n  /**\n   * Appx manifest created on disk - not packed into .appx package yet.\n   */\n  readonly appxManifestCreated?: ((path: string) => Promise<any> | any) | string | null\n  /**\n   * The function (or path to file or module id) to be [run on each node module](#onnodemodulefile) file. Returning `true`/`false` will determine whether to force include or to use the default copier logic\n   */\n  readonly onNodeModuleFile?: ((path: string) => void | boolean) | string | null\n  /**\n   * The function (or path to file or module id) to be run before dependencies are installed or rebuilt. Works when `npmRebuild` is set to `true`. Resolving to `false` will skip dependencies install or rebuild.\n   *\n   * If provided and `node_modules` are missing, it will not invoke production dependencies check.\n   */\n  readonly beforeBuild?: ((context: BeforeBuildContext) => Promise<any>) | string | null\n\n  /**\n   * Whether to include PDB files.\n   * @default false\n   */\n  readonly includePdb?: boolean\n\n  /**\n   * Whether to remove `scripts` field from `package.json` files.\n   *\n   * @default true\n   */\n  readonly removePackageScripts?: boolean\n\n  /**\n   * Whether to remove `keywords` field from `package.json` files.\n   *\n   * @default true\n   */\n  readonly removePackageKeywords?: boolean\n}\n\ninterface PackContext {\n  readonly outDir: string\n  readonly appOutDir: string\n  readonly packager: PlatformPackager<any>\n  readonly electronPlatformName: string\n  readonly arch: Arch\n  readonly targets: Array<Target>\n}\nexport type AfterPackContext = PackContext\nexport type BeforePackContext = PackContext\n\nexport interface MetadataDirectories {\n  /**\n   * The path to build resources.\n   *\n   * Please note — build resources are not packed into the app. If you need to use some files, e.g. as tray icon, please include required files explicitly: `\"files\": [\"**\\/*\", \"build/icon.*\"]`\n   * @default build\n   */\n  readonly buildResources?: string | null\n\n  /**\n   * The output directory. [File macros](/file-patterns#file-macros) are supported.\n   * @default dist\n   */\n  readonly output?: string | null\n\n  /**\n   * The application directory (containing the application package.json), defaults to `app`, `www` or working directory.\n   */\n  readonly app?: string | null\n}\n"]}