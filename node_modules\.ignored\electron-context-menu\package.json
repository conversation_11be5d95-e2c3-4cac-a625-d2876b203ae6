{"name": "electron-context-menu", "version": "3.6.1", "description": "Context menu for your Electron app", "license": "MIT", "repository": "sindresorhus/electron-context-menu", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "scripts": {"start": "electron fixtures/fixture.js", "test": "xo && ava && tsd", "start-fixture2": "electron fixtures/fixture-menu.js", "start-fixture3": "electron fixtures/fixture-toggle.js"}, "files": ["index.js", "index.d.ts"], "keywords": ["electron", "app", "context", "right-click", "menu", "extensible", "save", "image", "spellchecking", "spellcheck", "spelling", "spell", "check", "correct", "word", "words", "dictionary"], "dependencies": {"cli-truncate": "^2.1.0", "electron-dl": "^3.2.1", "electron-is-dev": "^2.0.0"}, "devDependencies": {"@types/node": "^15.0.1", "ava": "^2.4.0", "electron": "^12.0.6", "tsd": "^0.14.0", "xo": "^0.39.1"}, "xo": {"envs": ["node", "browser"]}, "tsd": {"compilerOptions": {"lib": ["es2019", "dom"]}}}