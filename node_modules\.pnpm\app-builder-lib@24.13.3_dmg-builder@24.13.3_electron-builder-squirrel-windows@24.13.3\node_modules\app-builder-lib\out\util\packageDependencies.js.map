{"version": 3, "file": "packageDependencies.js", "sourceRoot": "", "sources": ["../../src/util/packageDependencies.ts"], "names": [], "mappings": ";;;AAAA,uCAA+B;AAC/B,6CAAsD;AAEtD,SAAgB,wBAAwB,CAAC,UAAkB,EAAE,oBAA0C;IACrG,OAAO,IAAI,eAAI,CAAC,KAAK,IAAI,EAAE;QACzB,MAAM,IAAI,GAAG,CAAC,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC,CAAA;QACnD,IAAI,oBAAoB,IAAI,IAAI,EAAE,CAAC;YACjC,KAAK,MAAM,IAAI,IAAI,oBAAoB,EAAE,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;YAClC,CAAC;QACH,CAAC;QACD,OAAO,IAAA,oCAAuB,EAAa,IAAI,CAAC,CAAA;IAClD,CAAC,CAAC,CAAA;AACJ,CAAC;AAVD,4DAUC", "sourcesContent": ["import { Lazy } from \"lazy-val\"\nimport { executeAppBuilderAsJson } from \"./appBuilder\"\n\nexport function createLazyProductionDeps(projectDir: string, excludedDependencies: Array<string> | null) {\n  return new Lazy(async () => {\n    const args = [\"node-dep-tree\", \"--dir\", projectDir]\n    if (excludedDependencies != null) {\n      for (const name of excludedDependencies) {\n        args.push(\"--exclude-dep\", name)\n      }\n    }\n    return executeAppBuilderAsJson<Array<any>>(args)\n  })\n}\n\nexport interface NodeModuleDirInfo {\n  readonly dir: string\n  readonly deps: Array<NodeModuleInfo>\n}\n\nexport interface NodeModuleInfo {\n  readonly name: string\n}\n"]}