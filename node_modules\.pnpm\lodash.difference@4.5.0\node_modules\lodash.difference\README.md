# lodash.difference v4.5.0

The [lodash](https://lodash.com/) method `_.difference` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.difference
```

In Node.js:
```js
var difference = require('lodash.difference');
```

See the [documentation](https://lodash.com/docs#difference) or [package source](https://github.com/lodash/lodash/blob/4.5.0-npm-packages/lodash.difference) for more details.
