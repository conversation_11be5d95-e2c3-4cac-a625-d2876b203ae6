# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.1](https://github.com/inspect-js/buffer-equal/compare/v1.0.0...v1.0.1) - 2022-10-12

### Commits

- [eslint] fix indentation and whitespace [`94b97cd`](https://github.com/inspect-js/buffer-equal/commit/94b97cd8d3970fc8bd7d2eda3c074ccf7ee01659)
- [readme] rename, add badges [`c08b77f`](https://github.com/inspect-js/buffer-equal/commit/c08b77f67e02432cdc60fd8424661ec92251f3cf)
- [actions] add reusable workflows [`ea6a13d`](https://github.com/inspect-js/buffer-equal/commit/ea6a13ddf4e8dbd6c698787412540d4bd11a53c7)
- [Tests] switch from `tap` to `tape` [`d4681ac`](https://github.com/inspect-js/buffer-equal/commit/d4681aca8a5909467862e6ae0b2c91a6f27ba458)
- [meta] add `auto-changelog` [`50bd71a`](https://github.com/inspect-js/buffer-equal/commit/50bd71a3546e2e13f3c9af81b5f0c462efa897f1)
- [eslint] add eslint [`084e09e`](https://github.com/inspect-js/buffer-equal/commit/084e09ed8751d0e390ffb7937508b1aed448f897)
- [meta] create FUNDING.yml; add `funding` in package.json [`76b3e31`](https://github.com/inspect-js/buffer-equal/commit/76b3e317e16fad8f06e2bf727246028e7c2eb170)
- [meta] use `npmignore` to autogenerate an npmignore file [`514a3b7`](https://github.com/inspect-js/buffer-equal/commit/514a3b7e39b4eaf229b0d078ec05f156ae372a5f)
- Only apps should have lockfiles [`6e6963b`](https://github.com/inspect-js/buffer-equal/commit/6e6963baab8480210151e6216891bb9f6d880e76)
- [meta] update URLs [`0973f5a`](https://github.com/inspect-js/buffer-equal/commit/0973f5a9a3675476adaaecef634c6f5ac46080c1)
- [meta] add `safe-publish-latest` [`db19aab`](https://github.com/inspect-js/buffer-equal/commit/db19aabd4e38b07b7c5716006d9259fa2ef47ab1)
- [Tests] add `aud` in `posttest` [`b108d28`](https://github.com/inspect-js/buffer-equal/commit/b108d2849332f6e99cefdfcdd1aaed423a19717e)

## [v1.0.0](https://github.com/inspect-js/buffer-equal/compare/v0.0.2...v1.0.0) - 2016-01-01

## [v0.0.2](https://github.com/inspect-js/buffer-equal/compare/v0.0.1...v0.0.2) - 2016-01-01

### Commits

- add license file [`a7326b2`](https://github.com/inspect-js/buffer-equal/commit/a7326b2a0b34b98ba0d4d63cb126357175244847)

## [v0.0.1](https://github.com/inspect-js/buffer-equal/compare/v0.0.0...v0.0.1) - 2014-05-24

### Commits

- update node versions on travis [`0c246a1`](https://github.com/inspect-js/buffer-equal/commit/0c246a19136b82311e2289d629387a5e121796ef)
- depend on an old tap version so that travis passes again since they don't have the latest npm [`daf11fc`](https://github.com/inspect-js/buffer-equal/commit/daf11fca167cf6931f9a6a8071e508fdf015e09a)
- fix require() [`63e755d`](https://github.com/inspect-js/buffer-equal/commit/63e755dde568e0663314803c477de69efeea96af)
- Use node 0.11+ buffer.equals when available [`e0131f8`](https://github.com/inspect-js/buffer-equal/commit/e0131f82eb2f0fa0d0fcb72963d83f75ddc338c1)

## v0.0.0 - 2012-05-11

### Commits

- readme test and example [`98c6019`](https://github.com/inspect-js/buffer-equal/commit/98c601934a06da2ef2085574ae7ff6da3eebe8ad)
- added a package.json [`a8fc6c9`](https://github.com/inspect-js/buffer-equal/commit/a8fc6c93dfa95931eeaf5f0f4f6a9295c626a4a0)
- working implementation [`ee448ef`](https://github.com/inspect-js/buffer-equal/commit/ee448efb8e3550fd234e78213b34bff59ae04055)
- using travis [`2205205`](https://github.com/inspect-js/buffer-equal/commit/2205205c07a121cfabed2f696133638ded5302fa)
