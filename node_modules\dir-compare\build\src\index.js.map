{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,gDAA4B;AAC5B,4CAAmB;AACnB,gEAA+C;AAC/C,kEAAiD;AACjD,gHAAuF;AACvF,wFAAoF;AACpF,0FAAsF;AACtF,0FAAiE;AACjE,wEAA+C;AAC/C,2FAA6D;AAC7D,0EAAiD;AAIjD,MAAM,SAAS,GAAG,cAAS,CAAC,GAAG,CAAA;AAE/B,0CAAuB;AAGvB;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,KAAa,EAAE,KAAa,EAAE,OAAiB;IACvE,kEAAkE;IAClE,MAAM,aAAa,GAAG,cAAS,CAAC,SAAS,CAAC,cAAS,CAAC,OAAO,CAAC,YAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IACpF,MAAM,aAAa,GAAG,cAAS,CAAC,SAAS,CAAC,cAAS,CAAC,OAAO,CAAC,YAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;IACpF,IAAI,OAAO,CAAA;IACX,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,CAAA;IACjC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;QACpB,OAAO,GAAG,EAAE,CAAA;KACf;IACD,MAAM,UAAU,GAAG,6BAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;IACpD,qBAAmB,CACf,sBAAY,CAAC,UAAU,CAAC,aAAa,EAAE,KAAK,EAAE,cAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC,EACzF,sBAAY,CAAC,UAAU,CAAC,aAAa,EAAE,KAAK,EAAE,cAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,CAAC,EACzF,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,sBAAY,CAAC,gBAAgB,EAAE,CAAC,CAAA;IAChF,6BAAc,CAAC,kBAAkB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;IACtD,UAAU,CAAC,OAAO,GAAG,OAAO,CAAA;IAE5B,OAAO,UAA+B,CAAA;AAC1C,CAAC;AAlBD,kCAkBC;AAED;;;;;GAKG;AACH,SAAgB,OAAO,CAAC,KAAa,EAAE,KAAa,EAAE,OAAiB;IACnE,IAAI,aAAa,EAAE,aAAa,CAAA;IAChC,OAAO,OAAO,CAAC,OAAO,EAAE;SACnB,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;SAC3E,IAAI,CAAC,SAAS,CAAC,EAAE;QACd,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;QAC9B,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;QAC9B,8DAA8D;QAC9D,aAAa,GAAG,cAAS,CAAC,SAAS,CAAC,cAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAA;QACjE,aAAa,GAAG,cAAS,CAAC,SAAS,CAAC,cAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAA;IACrE,CAAC,CAAC;SACD,IAAI,CAAC,GAAG,EAAE;QACP,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,CAAA;QACjC,IAAI,YAAY,CAAA;QAChB,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACpB,YAAY,GAAG,EAAE,CAAA;SACpB;QACD,MAAM,UAAU,GAAG,6BAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;QACpD,OAAO,sBAAoB,CACvB,sBAAY,CAAC,UAAU,CAAC,aAAa,EAAE,KAAK,EAAE,cAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,EACjF,sBAAY,CAAC,UAAU,CAAC,aAAa,EAAE,KAAK,EAAE,cAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,EACjF,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,sBAAY,CAAC,gBAAgB,EAAE,CAAC;aAChF,IAAI,CAAC,GAAG,EAAE;YACP,6BAAc,CAAC,kBAAkB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;YACtD,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,SAAS,CAAA,EAAE;gBACrB,MAAM,OAAO,GAAG,EAAE,CAAA;gBAClB,mBAAmB,CAAC,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC,CAAA;gBACtD,UAAU,CAAC,OAAO,GAAG,OAAO,CAAA;aAC/B;YACD,OAAO,UAAU,CAAA;QACrB,CAAC,CAAC,CAAA;IACV,CAAC,CAAC,CAAA;AACV,CAAC;AAhCD,0BAgCC;AAED;;;GAGG;AACU,QAAA,mBAAmB,GAAwB;IACpD,kBAAkB,EAAlB,uCAAkB;IAClB,oBAAoB,EAApB,2CAAoB;CACvB,CAAA;AAID,MAAM,OAAO,GAAG;IACZ,QAAQ,CAAC,IAAY,EAAE,OAAyB;QAC5C,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,YAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE;gBAC7C,IAAI,GAAG,EAAE;oBACL,MAAM,CAAC,GAAG,CAAC,CAAA;iBACd;qBAAM;oBACH,OAAO,CAAC,YAAY,CAAC,CAAA;iBACxB;YACL,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;CACJ,CAAA;AAED,SAAS,cAAc,CAAC,OAAiB;IACrC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;IACvB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAA;IACjD,KAAK,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAA;IAC3C,KAAK,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAA;IAC/C,KAAK,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAA;IACjD,KAAK,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAA;IACrD,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;QACtB,KAAK,CAAC,aAAa,GAAG,sCAA4B,CAAA;KACrD;IACD,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;QACxB,KAAK,CAAC,eAAe,GAAG,uCAAkB,CAAC,WAAW,CAAA;KACzD;IACD,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;QACzB,KAAK,CAAC,gBAAgB,GAAG,uCAAkB,CAAC,YAAY,CAAA;KAC3D;IACD,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;QAC3B,KAAK,CAAC,kBAAkB,GAAG,4BAAkB,CAAA;KAChD;IACD,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,IAAI,IAAI,CAAA;IACjD,KAAK,CAAC,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;IACjD,IAAI,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,EAAE;QAC5B,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;KACpD;IACD,OAAO,KAAK,CAAA;AAChB,CAAC;AAGD,qDAAqD;AACrD,2DAA2D;AAC3D,SAAS,mBAAmB,CAAC,UAAU,EAAE,YAAY,EAAE,OAAO;IAC1D,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACzB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;SACxB;aAAM;YACH,mBAAmB,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;SACpD;IACL,CAAC,CAAC,CAAA;AACN,CAAC"}