{"version": 3, "file": "statisticsUpdate.js", "sourceRoot": "", "sources": ["../../../src/statistics/statisticsUpdate.js"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAC,OAAO,GAAG;IACb,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,qBAAqB,EAAE,UAAU,EAAE,OAAO;QAC/F,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAA;QACjD,IAAI,IAAI,KAAK,MAAM,EAAE;YACjB,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,aAAa,EAAE,CAAA;SAC9D;aAAM,IAAI,IAAI,KAAK,WAAW,EAAE;YAC7B,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,YAAY,EAAE,CAAA;SAC5D;aAAM,IAAI,IAAI,KAAK,aAAa,EAAE;YAC/B,UAAU,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAA;SAC/C;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAA;SAC7C;QAED,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,MAAM,SAAS,GAAG,UAAU,IAAI,UAAU,CAAA;QAC1C,IAAI,OAAO,CAAC,cAAc,IAAI,SAAS,EAAE;YACrC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAA;YACpC,IAAI,MAAM,KAAK,mBAAmB,EAAE;gBAChC,QAAQ,CAAC,gBAAgB,EAAE,CAAA;aAC9B;iBAAM;gBACH,QAAQ,CAAC,aAAa,EAAE,CAAA;aAC3B;SACJ;QAED,IAAI,qBAAqB,KAAK,mBAAmB,EAAE;YAC/C,UAAU,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAA;SACrD;aAAM,IAAI,qBAAqB,KAAK,oBAAoB,EAAE;YACvD,UAAU,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAA;SACtD;aAAM,IAAI,qBAAqB,KAAK,mBAAmB,EAAE;YACtD,UAAU,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,CAAA;SACzD;IACL,CAAC;IACD,oBAAoB,CAAC,MAAM,EAAE,IAAI,EAAE,qBAAqB,EAAE,UAAU,EAAE,OAAO;QACzE,UAAU,CAAC,IAAI,EAAE,CAAA;QACjB,IAAI,IAAI,KAAK,MAAM,EAAE;YACjB,UAAU,CAAC,SAAS,EAAE,CAAA;SACzB;aAAM,IAAI,IAAI,KAAK,WAAW,EAAE;YAC7B,UAAU,CAAC,QAAQ,EAAE,CAAA;SACxB;aAAM,IAAI,IAAI,KAAK,aAAa,EAAE;YAC/B,UAAU,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA;SAC3C;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAA;SAC7C;QAED,IAAI,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC,SAAS,EAAE;YAC5C,UAAU,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAA;SACrC;QAED,IAAI,qBAAqB,KAAK,mBAAmB,EAAE;YAC/C,UAAU,CAAC,gBAAgB,CAAC,oBAAoB,EAAE,CAAA;SACrD;IACL,CAAC;IACD,qBAAqB,CAAC,MAAM,EAAE,IAAI,EAAE,qBAAqB,EAAE,UAAU,EAAE,OAAO;QAC1E,UAAU,CAAC,KAAK,EAAE,CAAA;QAClB,IAAI,IAAI,KAAK,MAAM,EAAE;YACjB,UAAU,CAAC,UAAU,EAAE,CAAA;SAC1B;aAAM,IAAI,IAAI,KAAK,WAAW,EAAE;YAC7B,UAAU,CAAC,SAAS,EAAE,CAAA;SACzB;aAAM,IAAI,IAAI,KAAK,aAAa,EAAE;YAC/B,UAAU,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAA;SAC5C;aAAM;YACH,MAAM,IAAI,KAAK,CAAC,kBAAkB,GAAG,IAAI,CAAC,CAAA;SAC7C;QAED,IAAI,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC,SAAS,EAAE;YAC5C,UAAU,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAA;SACtC;QAED,IAAI,qBAAqB,KAAK,oBAAoB,EAAE;YAChD,UAAU,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAA;SACtD;IACL,CAAC;CACJ,CAAA"}