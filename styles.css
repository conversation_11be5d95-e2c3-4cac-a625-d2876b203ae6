/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    width: 100%;
    height: 100%;
    background: transparent;
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Main circular container */
#circle-container {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

#circle-container:hover {
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}

/* Video container and feed */
#video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

#video-feed {
    width: auto;
    height: 100%;
    min-width: 100%;
    object-fit: cover;
    transform: translateX(-50%);
    margin-left: 50%;
}

/* Connection status overlay */
.status-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    background: rgba(0, 0, 0, 0.7);
    padding: 20px;
    border-radius: 10px;
    backdrop-filter: blur(5px);
}

.status-text {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.status-subtitle {
    font-size: 12px;
    opacity: 0.8;
}

/* Settings overlay */
#settings-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    -webkit-app-region: no-drag;
}

.settings-content {
    width: 80%;
    max-height: 80%;
    overflow-y: auto;
    color: white;
    text-align: center;
}

.settings-content h3 {
    margin-bottom: 20px;
    color: #fff;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 12px;
    color: #ccc;
}

.setting-group input[type="text"],
.setting-group input[type="range"] {
    width: 100%;
    padding: 5px;
    border: 1px solid #555;
    border-radius: 3px;
    background: #333;
    color: white;
    font-size: 12px;
}

.radio-group {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    cursor: pointer;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
    -webkit-app-region: no-drag;
}

.btn.primary {
    background: #007acc;
    color: white;
}

.btn.primary:hover {
    background: #005a9e;
}

.btn.secondary {
    background: #555;
    color: white;
}

.btn.secondary:hover {
    background: #777;
}

/* Controls overlay */
#controls-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    z-index: 50;
}

.control-btn {
    width: 25px;
    height: 25px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    color: white;
    transition: background-color 0.2s;
    -webkit-app-region: no-drag;
}

.control-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* Resize handle */
#resize-handle {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 15px;
    height: 15px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    cursor: nw-resize;
    opacity: 0;
    transition: opacity 0.2s;
}

#circle-container:hover #resize-handle {
    opacity: 1;
}

/* Context menu */
.context-menu {
    position: fixed;
    background: rgba(0, 0, 0, 0.9);
    border: 1px solid #555;
    border-radius: 5px;
    padding: 5px 0;
    z-index: 1000;
    min-width: 120px;
    backdrop-filter: blur(10px);
    -webkit-app-region: no-drag;
}

.menu-item {
    padding: 8px 15px;
    color: white;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.menu-separator {
    height: 1px;
    background: #555;
    margin: 5px 0;
}

/* Utility classes */
.hidden {
    display: none !important;
}

/* Scrollbar styling for settings */
.settings-content::-webkit-scrollbar {
    width: 6px;
}

.settings-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.settings-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.settings-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
