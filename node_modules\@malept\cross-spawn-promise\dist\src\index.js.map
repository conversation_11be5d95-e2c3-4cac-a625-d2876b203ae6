{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";;;;;;AAAA,8DAAqC;AA4BrC,SAAS,gBAAgB,CAAC,GAAW,EAAE,IAA4B;IACjE,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QAClD,OAAO,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;KACnC;SAAM;QACL,OAAO,GAAG,CAAC;KACZ;AACH,CAAC;AAED;;GAEG;AACH,MAAa,eAAgB,SAAQ,KAAK;IAGxC,YACE,GAAW,EACX,IAAoB,EACpB,aAAoB,EACpB,MAAc;QAEd,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAChD,MAAM,YAAY,GAAG,aAAa,CAAC,OAAO,IAAI,aAAa,CAAC;QAC5D,KAAK,CACH,4BAA4B,WAAW,OAAO,YAAY,KAAK,MAAM,EAAE,CAAC,IAAI,EAAE,CAC/E,CAAC;QACF,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;CACF;AAhBD,0CAgBC;AAED;;GAEG;AACH,MAAsB,SAAU,SAAQ,KAAK;IAM3C,YACE,GAAW,EACX,IAAoB,EACpB,OAAe,EACf,MAAc,EACd,MAAc;QAEd,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;CACF;AAnBD,8BAmBC;AAED;;GAEG;AACH,MAAa,aAAc,SAAQ,SAAS;IAG1C,YACE,GAAW,EACX,IAAoB,EACpB,IAAY,EACZ,MAAc,EACd,MAAc;QAEd,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAChD,KAAK,CACH,GAAG,EACH,IAAI,EACJ,+CAA+C,IAAI,OAAO,WAAW,KAAK,MAAM,KAAK,MAAM,EAAE,CAAC,IAAI,EAAE,EACpG,MAAM,EACN,MAAM,CACP,CAAC;QACF,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;CACF;AApBD,sCAoBC;AAED;;GAEG;AACH,MAAa,eAAgB,SAAQ,SAAS;IAG5C,YACE,GAAW,EACX,IAAoB,EACpB,MAAc,EACd,MAAc,EACd,MAAc;QAEd,MAAM,WAAW,GAAG,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAChD,KAAK,CACH,GAAG,EACH,IAAI,EACJ,oCAAoC,MAAM,OAAO,WAAW,KAAK,MAAM,KAAK,MAAM,EAAE,CAAC,IAAI,EAAE,EAC3F,MAAM,EACN,MAAM,CACP,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;CACF;AApBD,0CAoBC;AAED;;;;;GAKG;AACI,KAAK,UAAU,KAAK,CACzB,GAAW,EACX,IAAqB,EACrB,OAA2B;IAE3B,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,GAAG,EAAuB,CAAC;KACnC;IACD,MAAM,EAAE,MAAM,EAAE,mBAAmB,EAAE,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC;IACjE,IAAI,MAAM;QAAE,MAAM,CAAC,qBAAqB,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAEvE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,MAAM,OAAO,GAAG,qBAAU,CAAC,GAAG,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;QACpD,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACjC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,CAAC,CAAC,CAAC;SACJ;QACD,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,CAAC,MAAM,CAAC,EAAE,CACf,MAAM;YACN,0BAA0B,CAAC,CAAC,IAAI,EAAE,EAAE;gBAClC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC5B,CAAC,CACF,CAAC;SACH;QACD,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;YACnC,IAAI,IAAI,KAAK,CAAC,EAAE;gBACd,OAAO,CAAC,MAAM,CAAC,CAAC;aACjB;iBAAM,IAAI,IAAI,KAAK,IAAI,EAAE;gBACxB,MAAM,CAAC,IAAI,eAAe,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;aAChE;iBAAM;gBACL,MAAM,CAAC,IAAI,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;aAC5D;QACH,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,EAAE;YAC1B,IAAI,mBAAmB,EAAE;gBACvB,mBAAmB,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;aACpC;YACD,MAAM,CAAC,IAAI,eAAe,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;QACtD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AA5CD,sBA4CC"}