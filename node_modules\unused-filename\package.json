{"name": "unused-filename", "version": "2.1.0", "description": "Get an unused filename by appending a number if it exists: `file.txt` → `file (1).txt`", "license": "MIT", "repository": "sindresorhus/unused-filename", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["unused", "filename", "filepath", "file", "name", "available", "safe", "unique", "usable", "filesystem", "fs", "exists", "path"], "dependencies": {"modify-filename": "^1.1.0", "path-exists": "^4.0.0"}, "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}