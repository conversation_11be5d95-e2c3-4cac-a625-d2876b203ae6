{"name": "webcam-circle", "productName": "WebcamCircle", "version": "0.0.2", "description": "Show your webcam view in a circle above all other windows", "license": "MIT", "repository": "cainhill/WebcamCircle", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://cainhill.com.au"}, "scripts": {"postinstall": "electron-builder install-app-deps", "lint": "xo", "test": "npm run lint", "start": "electron .", "pack": "electron-builder --dir", "dist": "electron-builder --windows", "release": "np"}, "dependencies": {"electron-context-menu": "^0.15.0", "electron-debug": "^3.0.0", "electron-store": "^5.1.0", "electron-unhandled": "^3.0.0", "electron-updater": "^6.6.2", "electron-util": "^0.13.0", "git": "^0.1.5", "webcamjs": "^1.0.26"}, "comment_1": "https://github.com/electron-userland/electron-builder/issues/4157#issuecomment-596419610", "devDependencies": {"electron": "^37.2.4", "electron-builder": "^26.0.12", "np": "^10.2.0", "xo": "^1.2.1"}, "xo": {"envs": ["node", "browser"]}, "np": {"publish": false, "releaseDraft": false}, "build": {"appId": "com.cainhill.WebcamCircle", "win": {"target": "portable", "icon": "build/icon.ico"}}}