{"name": "escape-goat", "version": "2.1.1", "description": "Escape a string for use in HTML or the inverse", "license": "MIT", "repository": "sindresorhus/escape-goat", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["escape", "unescape", "html", "entity", "entities", "escaping", "sanitize", "sanitization", "utility", "template", "attribute", "value", "interpolate", "xss", "goat", "🐐"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}