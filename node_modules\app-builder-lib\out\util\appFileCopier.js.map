{"version": 3, "file": "appFileCopier.js", "sourceRoot": "", "sources": ["../../src/util/appFileCopier.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAAoD;AACpD,4CAAyH;AAEzH,0CAA6C;AAC7C,uCAAwC;AACxC,6BAA4B;AAC5B,2DAAmD;AACnD,kCAAkC;AAClC,gDAA0D;AAC1D,wDAAqF;AAGrF,mDAA+C;AAC/C,iEAA6D;AAE7D,MAAM,wBAAwB,GAAG,GAAG,IAAI,CAAC,GAAG,mBAAmB,IAAI,CAAC,GAAG,EAAE,CAAA;AACzE,gBAAgB;AACH,QAAA,8BAA8B,GAAG,WAAW,CAAA;AAEzD,SAAgB,kBAAkB,CAAC,IAAY,EAAE,OAAwB;IACvE,IAAI,IAAI,KAAK,OAAO,CAAC,GAAG,EAAE,CAAC;QACzB,OAAO,OAAO,CAAC,WAAW,CAAA;IAC5B,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;QACvB,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,CAAA;QAChC,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;YACtF,OAAO,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QAC1C,CAAC;aAAM,CAAC;YACN,uBAAuB;YACvB,wIAAwI;YACxI,kGAAkG;YAClG,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,sCAAoB,CAAC,CAAA;YAC9C,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,GAAG,cAAc,CAAC,EAAE,CAAC;gBAC1D,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;YAC1B,CAAC;YACD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,SAAS,IAAI,qCAAqC,OAAO,CAAC,GAAG,GAAG,CAAC,CAAA;YACnF,CAAC;YACD,OAAO,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QACrC,CAAC;IACH,CAAC;AACH,CAAC;AAtBD,gDAsBC;AAEM,KAAK,UAAU,YAAY,CAAC,OAAwB,EAAE,QAAkB,EAAE,WAA4B;IAC3G,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;IACjC,2BAA2B;IAC3B,MAAM,WAAW,GAAG,IAAI,+BAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAA;IACpE,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAU,CAAA;IAE3C,MAAM,UAAU,GAAG,IAAI,eAAU,CAAC,IAAI,CAAC,EAAE;QACvC,oEAAoE;QACpE,OAAO,CAAC,CAAC,IAAA,2BAAU,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAA;IACtD,CAAC,EAAE,WAAW,CAAC,CAAA;IACf,MAAM,KAAK,GAAgB,EAAE,CAAA;IAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QACrD,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACnC,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;QACrC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM;YACN,SAAQ;QACV,CAAC;QAED,MAAM,eAAe,GAAG,kBAAkB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;QAC/D,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;YAC1B,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,IAAA,mBAAQ,EAAC,UAAU,CAAC,EAAE,CAAC,CAAA;YACvE,SAAQ;QACV,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;QAChD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YACvC,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;YACjC,MAAM,IAAA,gBAAK,EAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QAC9C,CAAC;QAED,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC,CAAA;QACvE,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,sBAAiB,EAAE,CAAC;YACjD,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAChC,CAAC;IACH,CAAC;IAED,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjC,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;IAChC,CAAC;IACD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,sBAAe,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,IAAA,wBAAa,EAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,gBAAW,CAAC,CAAA;IACtF,CAAC;AACH,CAAC;AA3CD,oCA2CC;AAYD,8DAA8D;AACvD,KAAK,UAAU,cAAc,CAAC,WAA4B,EAAE,OAAwB;IACzF,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;QACxB,OAAM;IACR,CAAC;IAED,IAAI,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAA;IAC/C,IAAI,OAAO,CAAC,gBAAgB,IAAI,IAAI,EAAE,CAAC;QACrC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAA;QAC5B,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;IAC7C,CAAC;IAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;IACjC,MAAM,sBAAe,CAAC,MAAM,CAC1B,OAAO,CAAC,KAAK,EACb,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;QACZ,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QACjC,IAAI,QAAQ,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;YAC3C,OAAO,KAAK,CAAA;QACd,CAAC;QAED,MAAM,gBAAgB,GAAG,WAAW,CAAC,EAAE,CAAC,CAAA;QACxC,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;YAC7B,OAAO,KAAK,CAAA;QACd,CAAC;QAED,IAAI,OAAO,gBAAgB,KAAK,QAAQ,IAAI,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACvE,OAAQ,gBAAiC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;gBAClD,IAAI,EAAE,IAAI,IAAI,EAAE,CAAC;oBACf,gBAAiB,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;gBAClC,CAAC;gBACD,OAAO,KAAK,CAAA;YACd,CAAC,CAAC,CAAA;QACJ,CAAC;QACD,gBAAiB,CAAC,GAAG,CAAC,KAAK,EAAE,gBAAmC,CAAC,CAAA;QACjE,OAAO,KAAK,CAAA;IACd,CAAC,EACD,gBAAW,CACZ,CAAA;AACH,CAAC;AAtCD,wCAsCC;AAEM,KAAK,UAAU,eAAe,CACnC,QAA4B,EAC5B,WAAmC,EACnC,gBAAuC,EACvC,iBAA0B;IAE1B,MAAM,QAAQ,GAA2B,EAAE,CAAA;IAC3C,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI,CAAA;IAEtC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,MAAM,UAAU,GAAG,IAAI,6BAAa,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;QAEvD,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAU,EAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAC/C,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,kBAAG,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,EAAE,iBAAiB,CAAC,CAAA;YAClF,SAAQ;QACV,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,IAAA,SAAI,EAAC,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;QACrE,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAA;QACpC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IACjG,CAAC;IAED,IAAI,iBAAiB,EAAE,CAAC;QACtB,0CAA0C;QAC1C,QAAQ,CAAC,OAAO,CAAC,MAAM,2BAA2B,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAA;IAC5E,CAAC;IACD,OAAO,QAAQ,CAAA;AACjB,CAAC;AA5BD,0CA4BC;AAED,SAAS,yBAAyB,CAAC,gBAAuC;IACxE,+FAA+F;IAC/F,MAAM,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,0BAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;IACjF,IAAI,gBAAgB,CAAC,MAAM,CAAC,UAAU,KAAK,IAAI,EAAE,CAAC;QAChD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACrB,CAAC;IACD,IAAI,gBAAgB,CAAC,QAAQ,KAAK,eAAQ,CAAC,OAAO,EAAE,CAAC;QACnD,oEAAoE;QACpE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACnB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IACrB,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,eAAe,CAAC,OAAwB;IAC/C,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACpD,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;IACzC,CAAC;IACD,OAAO,OAAO,CAAA;AAChB,CAAC;AAED,gBAAgB;AACT,KAAK,UAAU,yBAAyB,CAAC,gBAAuC,EAAE,WAAwB;IAC/G,MAAM,IAAI,GAAG,MAAM,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAA;IAC/F,MAAM,sBAAsB,GAAG,yBAAyB,CAAC,gBAAgB,CAAC,CAAA;IAC1E,oGAAoG;IACpG,MAAM,MAAM,GAAG,IAAI,KAAK,EAAmB,CAAA;IAC3C,IAAI,KAAK,GAAG,CAAC,CAAA;IACb,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAA;QACvB,MAAM,WAAW,GAAG,kBAAkB,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAW,EAAE,CAAC,CAAA;QAExI,0FAA0F;QAC1F,0JAA0J;QAC1J,MAAM,OAAO,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,WAAW,CAAC,aAAa,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAA;QACnH,MAAM,MAAM,GAAG,IAAI,2CAAoB,CAAC,OAAO,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAA;QACvE,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAC3C,MAAM,EACN,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,EAC5B,sBAAsB,CACvB,CAAA;QACD,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,eAAe,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAA;IACnG,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAtBD,8DAsBC;AAED,KAAK,UAAU,2BAA2B,CAAC,WAA4B,EAAE,QAAkB;IACzF,kBAAG,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAA;IAE5C,MAAM,oBAAoB,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,wBAAwB,EAAE,CAAC,CAAA;IAC3G,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAA;IAC1D,6BAA6B;IAC7B,MAAM,IAAA,gBAAK,EAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;IAC1C,MAAM,YAAY,GAAG,MAAM,IAAA,4CAA0B,EAAC,WAAW,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAA;IAChF,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAA;IACjD,kHAAkH;IAClH,MAAM,sBAAe,CAAC,GAAG,CACvB,WAAW,CAAC,KAAK,EACjB,IAAI,CAAC,EAAE;QACL,IACE,IAAI,CAAC,QAAQ,CAAC,sCAAoB,CAAC;YACnC,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC;YACvC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,IAAI,wBAAwB;YACpE,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC,MAAM,EAAE,EACzC,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;QACD,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAA;IACpD,CAAC,EACD,gBAAW,CACZ,CAAA;IAED,MAAM,YAAY,CAAC,iBAAiB,EAAE,CAAA;IAEtC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAiB,CAAA;IACzC,MAAM,UAAU,GAAG,MAAM,IAAA,SAAI,EAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;QACrE,OAAO,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,EAAE;YAC1B,IAAI,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;gBACtB,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;YAC9B,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;KACF,CAAC,CAAA;IAEF,WAAW;IACX,MAAM,QAAQ,GAAG,GAAG,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,sCAA8B,EAAE,CAAA;IACjF,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAChC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,KAAK,EAAE,cAAc,EAAE,GAAG,EAAE,CAAC,KAAK,EAAS,CAAC,CAAA;IACxH,IAAI,WAAW,CAAC,gBAAgB,IAAI,IAAI,EAAE,CAAC;QACzC,WAAW,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAA;IAC1C,CAAC;IACD,WAAW,CAAC,gBAAgB,CAAC,GAAG,CAC9B,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAC5B;;kFAE8E,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,OAAO;CAClH,CACE,CAAA;IACD,OAAO,EAAE,GAAG,EAAE,oBAAoB,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,CAAC,WAAW,EAAE,CAAA;AACzG,CAAC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { AsyncTask<PERSON>anager, log } from \"builder-util\"\nimport { CONCURRENCY, FileCopier, FileTransformer, Link, MAX_FILE_REQUESTS, statOrNull, walk } from \"builder-util/out/fs\"\nimport { Stats } from \"fs\"\nimport { mkdir, readlink } from \"fs/promises\"\nimport { ensureSymlink } from \"fs-extra\"\nimport * as path from \"path\"\nimport { isLibOrExe } from \"../asar/unpackDetector\"\nimport { Platform } from \"../core\"\nimport { excludedExts, FileMatcher } from \"../fileMatcher\"\nimport { createElectronCompilerHost, NODE_MODULES_PATTERN } from \"../fileTransformer\"\nimport { Packager } from \"../packager\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { AppFileWalker } from \"./AppFileWalker\"\nimport { NodeModuleCopyHelper } from \"./NodeModuleCopyHelper\"\n\nconst BOWER_COMPONENTS_PATTERN = `${path.sep}bower_components${path.sep}`\n/** @internal */\nexport const ELECTRON_COMPILE_SHIM_FILENAME = \"__shim.js\"\n\nexport function getDestinationPath(file: string, fileSet: ResolvedFileSet) {\n  if (file === fileSet.src) {\n    return fileSet.destination\n  } else {\n    const src = fileSet.src\n    const dest = fileSet.destination\n    if (file.length > src.length && file.startsWith(src) && file[src.length] === path.sep) {\n      return dest + file.substring(src.length)\n    } else {\n      // hoisted node_modules\n      // not lastIndexOf, to ensure that nested module (top-level module depends on) copied to parent node_modules, not to top-level directory\n      // project https://github.com/angexis/punchcontrol/commit/cf929aba55c40d0d8901c54df7945e1d001ce022\n      let index = file.indexOf(NODE_MODULES_PATTERN)\n      if (index < 0 && file.endsWith(`${path.sep}node_modules`)) {\n        index = file.length - 13\n      }\n      if (index < 0) {\n        throw new Error(`File \"${file}\" not under the source directory \"${fileSet.src}\"`)\n      }\n      return dest + file.substring(index)\n    }\n  }\n}\n\nexport async function copyAppFiles(fileSet: ResolvedFileSet, packager: Packager, transformer: FileTransformer) {\n  const metadata = fileSet.metadata\n  // search auto unpacked dir\n  const taskManager = new AsyncTaskManager(packager.cancellationToken)\n  const createdParentDirs = new Set<string>()\n\n  const fileCopier = new FileCopier(file => {\n    // https://github.com/electron-userland/electron-builder/issues/3038\n    return !(isLibOrExe(file) || file.endsWith(\".node\"))\n  }, transformer)\n  const links: Array<Link> = []\n  for (let i = 0, n = fileSet.files.length; i < n; i++) {\n    const sourceFile = fileSet.files[i]\n    const stat = metadata.get(sourceFile)\n    if (stat == null) {\n      // dir\n      continue\n    }\n\n    const destinationFile = getDestinationPath(sourceFile, fileSet)\n    if (stat.isSymbolicLink()) {\n      links.push({ file: destinationFile, link: await readlink(sourceFile) })\n      continue\n    }\n\n    const fileParent = path.dirname(destinationFile)\n    if (!createdParentDirs.has(fileParent)) {\n      createdParentDirs.add(fileParent)\n      await mkdir(fileParent, { recursive: true })\n    }\n\n    taskManager.addTask(fileCopier.copy(sourceFile, destinationFile, stat))\n    if (taskManager.tasks.length > MAX_FILE_REQUESTS) {\n      await taskManager.awaitTasks()\n    }\n  }\n\n  if (taskManager.tasks.length > 0) {\n    await taskManager.awaitTasks()\n  }\n  if (links.length > 0) {\n    await BluebirdPromise.map(links, it => ensureSymlink(it.link, it.file), CONCURRENCY)\n  }\n}\n\n// os path separator is used\nexport interface ResolvedFileSet {\n  src: string\n  destination: string\n\n  files: Array<string>\n  metadata: Map<string, Stats>\n  transformedFiles?: Map<number, string | Buffer> | null\n}\n\n// used only for ASAR, if no asar, file transformed on the fly\nexport async function transformFiles(transformer: FileTransformer, fileSet: ResolvedFileSet): Promise<void> {\n  if (transformer == null) {\n    return\n  }\n\n  let transformedFiles = fileSet.transformedFiles\n  if (fileSet.transformedFiles == null) {\n    transformedFiles = new Map()\n    fileSet.transformedFiles = transformedFiles\n  }\n\n  const metadata = fileSet.metadata\n  await BluebirdPromise.filter(\n    fileSet.files,\n    (it, index) => {\n      const fileStat = metadata.get(it)\n      if (fileStat == null || !fileStat.isFile()) {\n        return false\n      }\n\n      const transformedValue = transformer(it)\n      if (transformedValue == null) {\n        return false\n      }\n\n      if (typeof transformedValue === \"object\" && \"then\" in transformedValue) {\n        return (transformedValue as Promise<any>).then(it => {\n          if (it != null) {\n            transformedFiles!.set(index, it)\n          }\n          return false\n        })\n      }\n      transformedFiles!.set(index, transformedValue as string | Buffer)\n      return false\n    },\n    CONCURRENCY\n  )\n}\n\nexport async function computeFileSets(\n  matchers: Array<FileMatcher>,\n  transformer: FileTransformer | null,\n  platformPackager: PlatformPackager<any>,\n  isElectronCompile: boolean\n): Promise<Array<ResolvedFileSet>> {\n  const fileSets: Array<ResolvedFileSet> = []\n  const packager = platformPackager.info\n\n  for (const matcher of matchers) {\n    const fileWalker = new AppFileWalker(matcher, packager)\n\n    const fromStat = await statOrNull(matcher.from)\n    if (fromStat == null) {\n      log.debug({ directory: matcher.from, reason: \"doesn't exist\" }, `skipped copying`)\n      continue\n    }\n\n    const files = await walk(matcher.from, fileWalker.filter, fileWalker)\n    const metadata = fileWalker.metadata\n    fileSets.push(validateFileSet({ src: matcher.from, files, metadata, destination: matcher.to }))\n  }\n\n  if (isElectronCompile) {\n    // cache files should be first (better IO)\n    fileSets.unshift(await compileUsingElectronCompile(fileSets[0], packager))\n  }\n  return fileSets\n}\n\nfunction getNodeModuleExcludedExts(platformPackager: PlatformPackager<any>) {\n  // do not exclude *.h files (https://github.com/electron-userland/electron-builder/issues/2852)\n  const result = [\".o\", \".obj\"].concat(excludedExts.split(\",\").map(it => `.${it}`))\n  if (platformPackager.config.includePdb !== true) {\n    result.push(\".pdb\")\n  }\n  if (platformPackager.platform !== Platform.WINDOWS) {\n    // https://github.com/electron-userland/electron-builder/issues/1738\n    result.push(\".dll\")\n    result.push(\".exe\")\n  }\n  return result\n}\n\nfunction validateFileSet(fileSet: ResolvedFileSet): ResolvedFileSet {\n  if (fileSet.src == null || fileSet.src.length === 0) {\n    throw new Error(\"fileset src is empty\")\n  }\n  return fileSet\n}\n\n/** @internal */\nexport async function computeNodeModuleFileSets(platformPackager: PlatformPackager<any>, mainMatcher: FileMatcher): Promise<Array<ResolvedFileSet>> {\n  const deps = await platformPackager.info.getNodeDependencyInfo(platformPackager.platform).value\n  const nodeModuleExcludedExts = getNodeModuleExcludedExts(platformPackager)\n  // serial execution because copyNodeModules is concurrent and so, no need to increase queue/pressure\n  const result = new Array<ResolvedFileSet>()\n  let index = 0\n  for (const info of deps) {\n    const source = info.dir\n    const destination = getDestinationPath(source, { src: mainMatcher.from, destination: mainMatcher.to, files: [], metadata: null as any })\n\n    // use main matcher patterns, so, user can exclude some files in such hoisted node modules\n    // source here includes node_modules, but pattern base should be without because users expect that pattern \"!node_modules/loot-core/src{,/**/*}\" will work\n    const matcher = new FileMatcher(path.dirname(source), destination, mainMatcher.macroExpander, mainMatcher.patterns)\n    const copier = new NodeModuleCopyHelper(matcher, platformPackager.info)\n    const files = await copier.collectNodeModules(\n      source,\n      info.deps.map(it => it.name),\n      nodeModuleExcludedExts\n    )\n    result[index++] = validateFileSet({ src: source, destination, files, metadata: copier.metadata })\n  }\n  return result\n}\n\nasync function compileUsingElectronCompile(mainFileSet: ResolvedFileSet, packager: Packager): Promise<ResolvedFileSet> {\n  log.info(\"compiling using electron-compile\")\n\n  const electronCompileCache = await packager.tempDirManager.getTempDir({ prefix: \"electron-compile-cache\" })\n  const cacheDir = path.join(electronCompileCache, \".cache\")\n  // clear and create cache dir\n  await mkdir(cacheDir, { recursive: true })\n  const compilerHost = await createElectronCompilerHost(mainFileSet.src, cacheDir)\n  const nextSlashIndex = mainFileSet.src.length + 1\n  // pre-compute electron-compile to cache dir - we need to process only subdirectories, not direct files of app dir\n  await BluebirdPromise.map(\n    mainFileSet.files,\n    file => {\n      if (\n        file.includes(NODE_MODULES_PATTERN) ||\n        file.includes(BOWER_COMPONENTS_PATTERN) ||\n        !file.includes(path.sep, nextSlashIndex) || // ignore not root files\n        !mainFileSet.metadata.get(file)!.isFile()\n      ) {\n        return null\n      }\n      return compilerHost.compile(file).then(() => null)\n    },\n    CONCURRENCY\n  )\n\n  await compilerHost.saveConfiguration()\n\n  const metadata = new Map<string, Stats>()\n  const cacheFiles = await walk(cacheDir, file => !file.startsWith(\".\"), {\n    consume: (file, fileStat) => {\n      if (fileStat.isFile()) {\n        metadata.set(file, fileStat)\n      }\n      return null\n    },\n  })\n\n  // add shim\n  const shimPath = `${mainFileSet.src}${path.sep}${ELECTRON_COMPILE_SHIM_FILENAME}`\n  mainFileSet.files.push(shimPath)\n  mainFileSet.metadata.set(shimPath, { isFile: () => true, isDirectory: () => false, isSymbolicLink: () => false } as any)\n  if (mainFileSet.transformedFiles == null) {\n    mainFileSet.transformedFiles = new Map()\n  }\n  mainFileSet.transformedFiles.set(\n    mainFileSet.files.length - 1,\n    `\n'use strict';\nrequire('electron-compile').init(__dirname, require('path').resolve(__dirname, '${packager.metadata.main || \"index\"}'), true);\n`\n  )\n  return { src: electronCompileCache, files: cacheFiles, metadata, destination: mainFileSet.destination }\n}\n"]}