export function detectLoop(entry: any, symlinkCache: any): boolean;
export function detectLoop(entry: any, symlinkCache: any): boolean;
export function initSymlinkCache(): {
    dir1: {};
    dir2: {};
};
export function initSymlinkCache(): {
    dir1: {};
    dir2: {};
};
export function updateSymlinkCache(symlinkCache: any, rootEntry1: any, rootEntry2: any, loopDetected1: any, loopDetected2: any): void;
export function updateSymlinkCache(symlinkCache: any, rootEntry1: any, rootEntry2: any, loopDetected1: any, loopDetected2: any): void;
export function cloneSymlinkCache(symlinkCache: any): {
    dir1: {};
    dir2: {};
};
export function cloneSymlinkCache(symlinkCache: any): {
    dir1: {};
    dir2: {};
};
//# sourceMappingURL=loopDetector.d.ts.map