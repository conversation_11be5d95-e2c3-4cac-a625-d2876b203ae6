<?xml version="1.0" encoding="utf-8"?>
<!--suppress XmlUnusedNamespaceDeclaration -->
<Package
   xmlns="http://schemas.microsoft.com/appx/manifest/foundation/windows10"
   xmlns:uap="http://schemas.microsoft.com/appx/manifest/uap/windows10"
   xmlns:desktop="http://schemas.microsoft.com/appx/manifest/desktop/windows10"
   xmlns:rescap="http://schemas.microsoft.com/appx/manifest/foundation/windows10/restrictedcapabilities">
  <!-- use single quotes to avoid double quotes escaping in the publisher value  -->
  <Identity Name="${identityName}"
    ProcessorArchitecture="${arch}"
    Publisher='${publisher}'
    Version="${version}" />
  <Properties>
    <DisplayName>${displayName}</DisplayName>
    <PublisherDisplayName>${publisherDisplayName}</PublisherDisplayName>
    <Description>${description}</Description>
    <Logo>${logo}</Logo>
  </Properties>
  <Resources>
    ${resourceLanguages}
  </Resources>
  <Dependencies>
    <TargetDeviceFamily Name="Windows.Desktop" MinVersion="${minVersion}" MaxVersionTested="${maxVersionTested}" />
  </Dependencies>
  <Capabilities>
    <rescap:Capability Name="runFullTrust"/>
  </Capabilities>
  <Applications>
    <Application Id="${applicationId}" Executable="${executable}" EntryPoint="Windows.FullTrustApplication">
      <uap:VisualElements
       BackgroundColor="${backgroundColor}"
       DisplayName="${displayName}"
       Square150x150Logo="${square150x150Logo}"
       Square44x44Logo="${square44x44Logo}"
       Description="${description}">
        ${lockScreen}
        ${defaultTile}
        ${splashScreen}
      </uap:VisualElements>
      ${extensions}
    </Application>
  </Applications>
</Package>
