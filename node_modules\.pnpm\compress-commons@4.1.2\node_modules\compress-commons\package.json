{"name": "compress-commons", "version": "4.1.2", "description": "a library that defines a common interface for working with archive formats within node", "homepage": "https://github.com/archiverjs/node-compress-commons", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/node-compress-commons.git"}, "bugs": {"url": "https://github.com/archiverjs/node-compress-commons/issues"}, "license": "MIT", "main": "lib/compress-commons.js", "files": ["lib"], "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"buffer-crc32": "^0.2.13", "crc32-stream": "^4.0.2", "normalize-path": "^3.0.0", "readable-stream": "^3.6.0"}, "devDependencies": {"chai": "4.3.8", "mkdirp": "2.1.6", "mocha": "9.2.2", "rimraf": "3.0.2"}, "keywords": ["compress", "commons", "archive"]}