<!doctype html>
<html>
	<head>
		<meta charset="utf-8">
		<title>WebcamCircle</title>
		<link rel="stylesheet" href="index.css">
	</head>
	<body style="-webkit-app-region: drag">
			<div class="controls">
				<p>Select your webcam:</p>
				<select id="select"></select>
				<button id="button">Start</button>
			</div>
			<div id="crop" class="hide">
				<video id="video" autoplay="" playsinline=""></video>
			</div>
	</body>
	<style>
		/* https://www.electronjs.org/docs/api/frameless-window */
		button, select {
			-webkit-app-region: no-drag;
		}
		p {
			padding-top: 10px;
	    margin: 0;
		}
		.controls {
			text-align: center;
			width: 300px;
			height: 300px;
			background: #f2f2f2;
			margin: 15px auto;
		}
		.hide {
			display: none;
		}
		#select {
			display: block;
			margin: 10px auto;
			width: 250px;
		}
		/* https://stackoverflow.com/questions/15167545/how-to-crop-a-rectangular-image-into-a-square-with-css */
		#crop {
			border-radius: 50%;
	    overflow: hidden;
	    width: 300px;
	    height: 300px;
	    margin: 0 auto;
		}
		#video {
			position: relative;
	    height: 100% !important;
	    width: auto !important;
	    left: 50%;
	    transform: translate3d( -50%, 0, 0 );
		}
	</style>
	<script>
		// https://github.com/philnash/mediadevices-camera-selection/blob/master/app.js
		const video = document.getElementById('video');
		const button = document.getElementById('button');
		const select = document.getElementById('select');

		// CH: These next two lines added
		const controls = document.querySelector('.controls');
		const crop = document.getElementById('crop');

		let currentStream;

		function stopMediaTracks(stream) {
		  stream.getTracks().forEach(track => {
		    track.stop();
		  });
		}

		function gotDevices(mediaDevices) {
		  select.innerHTML = '';
		  select.appendChild(document.createElement('option'));
		  let count = 1;
		  mediaDevices.forEach(mediaDevice => {
		    if (mediaDevice.kind === 'videoinput') {
		      const option = document.createElement('option');
		      option.value = mediaDevice.deviceId;
		      const label = mediaDevice.label || `Camera ${count++}`;
		      const textNode = document.createTextNode(label);
		      option.appendChild(textNode);
		      select.appendChild(option);
		    }
		  });
		}

		button.addEventListener('click', event => {
		  if (typeof currentStream !== 'undefined') {
		    stopMediaTracks(currentStream);
		  }
		  const videoConstraints = {};
		  if (select.value === '') {
		    videoConstraints.facingMode = 'environment';
		  } else {
		    videoConstraints.deviceId = { exact: select.value };
		  }
		  const constraints = {
		    video: videoConstraints,
		    audio: false
		  };
		  navigator.mediaDevices
		    .getUserMedia(constraints)
		    .then(stream => {
		      currentStream = stream;
		      video.srcObject = stream;

					// CH: These next two lines added
					controls.classList.add('hide');
					crop.classList.remove('hide');

		      return navigator.mediaDevices.enumerateDevices();
		    })
		    .then(gotDevices)
		    .catch(error => {
		      console.error(error);
		    });
		});

		navigator.mediaDevices.enumerateDevices().then(gotDevices);
	</script>



























<!--

	<script>
		// https://github.com/webrtc/samples/tree/gh-pages/src/content/devices/input-output
		// https://ourcodeworld.com/articles/read/134/how-to-use-the-camera-with-electron-framework-create-a-snapshot-and-save-the-image-on-the-system
		// A flag to know when start or stop the camera
		var enabled = false;
		// Use require to add webcamjs
		var WebCamera = require("webcamjs");

		document.getElementById("start").addEventListener('click',function(){
		   if(!enabled){ // Start the camera !
		     enabled = true;
		     WebCamera.attach('#camdemo');
		     console.log("The camera has been started");
		   }else{ // Disable the camera !
		     enabled = false;
		     WebCamera.reset();
		    console.log("The camera has been disabled");
		   }
		},false);
	</script>
	<style>
		div {
			border-radius: 50%;
			overflow: hidden;
		}
		/* https://stackoverflow.com/questions/15167545/how-to-crop-a-rectangular-image-into-a-square-with-css */
		div video {
			position: relative;
			height: 100% !important;
			width: auto !important;
			left: 50%;
			transform: translate3d( -50%, 0, 0 );
		}
		/* https://www.electronjs.org/docs/api/frameless-window */
		input[type="button"] {
		  -webkit-app-region: no-drag;
		}
	</style> -->
</html>
