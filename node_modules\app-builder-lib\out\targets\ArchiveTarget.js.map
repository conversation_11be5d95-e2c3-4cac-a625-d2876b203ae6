{"version": 3, "file": "ArchiveTarget.js", "sourceRoot": "", "sources": ["../../src/targets/ArchiveTarget.ts"], "names": [], "mappings": ";;;AAAA,+CAA0D;AAC1D,6BAA4B;AAC5B,kCAAiE;AACjE,gDAA2D;AAE3D,uCAAwC;AACxC,mFAAgF;AAEhF,MAAa,aAAc,SAAQ,aAAM;IAGvC,YACE,IAAY,EACH,MAAc,EACN,QAA+B,EAC/B,oBAAoB,KAAK;QAE1C,KAAK,CAAC,IAAI,CAAC,CAAA;QAJF,WAAM,GAAN,MAAM,CAAQ;QACN,aAAQ,GAAR,QAAQ,CAAuB;QAC/B,sBAAiB,GAAjB,iBAAiB,CAAQ;QANnC,YAAO,GAA2B,IAAI,CAAC,QAAQ,CAAC,MAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IASlF,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,SAAiB,EAAE,IAAU;QACvC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,KAAK,GAAG,QAAQ,CAAC,QAAQ,KAAK,eAAQ,CAAC,GAAG,CAAA;QAChD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;QAExB,IAAI,cAAsB,CAAA;QAC1B,MAAM,WAAW,GAAS,IAAA,oCAAqB,EAAC,QAAQ,CAAC,4BAA4B,CAAC,WAAW,CAAC,CAAA;QAClG,IAAI,QAAQ,CAAC,QAAQ,KAAK,eAAQ,CAAC,KAAK,EAAE,CAAC;YACzC,uDAAuD;YACvD,cAAc,GAAG,oBAAoB,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,SAAS,CAAA;QAC9F,CAAC;aAAM,CAAC;YACN,uDAAuD;YACvD,cAAc,GAAG,2BAA2B,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,eAAe,CAAA;QAC3G,CAAC;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,CAAC,CAAA;QAC1G,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QAEzD,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC3C,qBAAqB,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,EAAE;YAC1D,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,CAAC,CAAA;QACF,IAAI,UAAU,GAAQ,IAAI,CAAA;QAC1B,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAA,aAAG,EAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QACvG,CAAC;aAAM,CAAC;YACN,IAAI,UAAU,GAAG,CAAC,KAAK,CAAA;YACvB,IAAI,YAAY,GAAG,SAAS,CAAA;YAC5B,IAAI,KAAK,EAAE,CAAC;gBACV,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;gBACtC,MAAM,YAAY,GAAG,IAAA,6BAAe,EAClC,QAAQ,CAAC,MAAM,EACf,gBAAgB,EAChB,YAAY,EACZ,QAAQ,CAAC,4BAA4B,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,CAAC,4BAA4B,CAAC,CAChG,CAAA;gBACD,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;oBACzB,YAAY,GAAG,SAAS,CAAA;gBAC1B,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAA,uBAAS,EAAC,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;oBACzC,UAAU,GAAG,IAAI,CAAA;gBACnB,CAAC;YACH,CAAC;YAED,MAAM,cAAc,GAAG;gBACrB,WAAW,EAAE,QAAQ,CAAC,WAAW;gBACjC,UAAU;aACX,CAAA;YACD,MAAM,IAAA,iBAAO,EAAC,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC,CAAA;YAEjE,IAAI,IAAI,CAAC,iBAAiB,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;gBAC/C,IAAI,KAAK,EAAE,CAAC;oBACV,UAAU,GAAG,MAAM,IAAA,8CAAc,EAAC,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAA;gBAC/E,CAAC;qBAAM,CAAC;oBACN,UAAU,GAAG,MAAM,IAAA,8CAAc,EAAC,YAAY,CAAC,CAAA;gBACjD,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC7C,UAAU;YACV,IAAI,EAAE,YAAY;YAClB,uDAAuD;YACvD,gBAAgB,EAAE,QAAQ,CAAC,uBAAuB,CAChD,YAAY,EACZ,MAAM,EACN,IAAI,EACJ,KAAK,EACL,QAAQ,CAAC,4BAA4B,CAAC,WAAW,EACjD,cAAc,CAAC,OAAO,CAAC,gBAAgB,EAAE,SAAS,CAAC,CACpD;YACD,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,QAAQ;YACR,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;SAC1C,CAAC,CAAA;IACJ,CAAC;CACF;AA1FD,sCA0FC", "sourcesContent": ["import { Arch, defaultArchFromString } from \"builder-util\"\nimport * as path from \"path\"\nimport { Platform, Target, TargetSpecificOptions } from \"../core\"\nimport { copyFiles, getFileMatchers } from \"../fileMatcher\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { archive, tar } from \"./archive\"\nimport { appendBlockmap, createBlockmap } from \"./differentialUpdateInfoBuilder\"\n\nexport class ArchiveTarget extends Target {\n  readonly options: TargetSpecificOptions = (this.packager.config as any)[this.name]\n\n  constructor(\n    name: string,\n    readonly outDir: string,\n    private readonly packager: PlatformPackager<any>,\n    private readonly isWriteUpdateInfo = false\n  ) {\n    super(name)\n  }\n\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    const packager = this.packager\n    const isMac = packager.platform === Platform.MAC\n    const format = this.name\n\n    let defaultPattern: string\n    const defaultArch: Arch = defaultArchFromString(packager.platformSpecificBuildOptions.defaultArch)\n    if (packager.platform === Platform.LINUX) {\n      // tslint:disable-next-line:no-invalid-template-strings\n      defaultPattern = \"${name}-${version}\" + (arch === defaultArch ? \"\" : \"-${arch}\") + \".${ext}\"\n    } else {\n      // tslint:disable-next-line:no-invalid-template-strings\n      defaultPattern = \"${productName}-${version}\" + (arch === defaultArch ? \"\" : \"-${arch}\") + \"-${os}.${ext}\"\n    }\n\n    const artifactName = packager.expandArtifactNamePattern(this.options, format, arch, defaultPattern, false)\n    const artifactPath = path.join(this.outDir, artifactName)\n\n    await packager.info.callArtifactBuildStarted({\n      targetPresentableName: `${isMac ? \"macOS \" : \"\"}${format}`,\n      file: artifactPath,\n      arch,\n    })\n    let updateInfo: any = null\n    if (format.startsWith(\"tar.\")) {\n      await tar(packager.compression, format, artifactPath, appOutDir, isMac, packager.info.tempDirManager)\n    } else {\n      let withoutDir = !isMac\n      let dirToArchive = appOutDir\n      if (isMac) {\n        dirToArchive = path.dirname(appOutDir)\n        const fileMatchers = getFileMatchers(\n          packager.config,\n          \"extraDistFiles\",\n          dirToArchive,\n          packager.createGetFileMatchersOptions(this.outDir, arch, packager.platformSpecificBuildOptions)\n        )\n        if (fileMatchers == null) {\n          dirToArchive = appOutDir\n        } else {\n          await copyFiles(fileMatchers, null, true)\n          withoutDir = true\n        }\n      }\n\n      const archiveOptions = {\n        compression: packager.compression,\n        withoutDir,\n      }\n      await archive(format, artifactPath, dirToArchive, archiveOptions)\n\n      if (this.isWriteUpdateInfo && format === \"zip\") {\n        if (isMac) {\n          updateInfo = await createBlockmap(artifactPath, this, packager, artifactName)\n        } else {\n          updateInfo = await appendBlockmap(artifactPath)\n        }\n      }\n    }\n\n    await packager.info.callArtifactBuildCompleted({\n      updateInfo,\n      file: artifactPath,\n      // tslint:disable-next-line:no-invalid-template-strings\n      safeArtifactName: packager.computeSafeArtifactName(\n        artifactName,\n        format,\n        arch,\n        false,\n        packager.platformSpecificBuildOptions.defaultArch,\n        defaultPattern.replace(\"${productName}\", \"${name}\")\n      ),\n      target: this,\n      arch,\n      packager,\n      isWriteUpdateInfo: this.isWriteUpdateInfo,\n    })\n  }\n}\n"]}