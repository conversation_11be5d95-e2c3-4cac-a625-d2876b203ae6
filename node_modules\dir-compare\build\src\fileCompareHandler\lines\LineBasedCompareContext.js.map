{"version": 3, "file": "LineBasedCompareContext.js", "sourceRoot": "", "sources": ["../../../../src/fileCompareHandler/lines/LineBasedCompareContext.ts"], "names": [], "mappings": ";;;AAYA,MAAa,uBAAuB;IAwBhC,YAAY,GAAW,EAAE,GAAW,EAAE,UAAsB;QAX5D;;;WAGG;QACI,SAAI,GAAa,EAAC,KAAK,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAC,CAAA;QAC9C;;;WAGG;QACI,cAAS,GAAc,EAAC,UAAU,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAC,CAAA;QAG1D,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;QACd,IAAI,CAAC,MAAM,GAAG,UAAU,CAAA;IAC5B,CAAC;CACJ;AA7BD,0DA6BC"}