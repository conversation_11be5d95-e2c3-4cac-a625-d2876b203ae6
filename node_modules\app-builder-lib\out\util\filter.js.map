{"version": 3, "file": "filter.js", "sourceRoot": "", "sources": ["../../src/util/filter.ts"], "names": [], "mappings": ";;;AAGA,6BAA4B;AAC5B,wDAAyD;AAEzD,gBAAgB;AAChB,SAAgB,QAAQ,CAAC,OAAkB;IACzC,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;IACvB,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACnB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,KAAK,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QACvB,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAA;QACb,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAA;AACd,CAAC;AAbD,4BAaC;AAED,6JAA6J;AAC7J,SAAS,cAAc,CAAC,CAAS;IAC/B,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAA;AAClE,CAAC;AAED,SAAS,eAAe,CAAC,IAAY,EAAE,eAAuB;IAC5D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;QACtC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,sCAAoB,CAAC,CAAA;QAChD,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,kBAAkB,eAAe,EAAE,CAAC,CAAA;QAC7D,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,mBAAmB,CAAC,CAAA;QACtD,CAAC;IACH,CAAC;IAED,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;IACrD,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE,CAAC;QACtB,IAAI,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAC9B,mIAAmI;YACnI,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QAClC,CAAC;QACD,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;IACzC,CAAC;IACD,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED,gBAAgB;AAChB,SAAgB,YAAY,CAAC,GAAW,EAAE,QAA0B,EAAE,eAAyC;IAC7G,MAAM,eAAe,GAAG,cAAc,CAAC,GAAG,CAAC,CAAA;IAC3C,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;QACpB,IAAI,GAAG,KAAK,IAAI,EAAE,CAAC;YACjB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,EAAE,eAAe,CAAC,CAAA;QACvD,mEAAmE;QACnE,OAAO,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC,CAAA;IACpJ,CAAC,CAAA;AACH,CAAC;AAXD,oCAWC;AAED,gEAAgE;AAChE,SAAS,YAAY,CAAC,IAAY,EAAE,QAA0B,EAAE,IAAW;IACzE,IAAI,KAAK,GAAG,KAAK,CAAA;IACjB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,qDAAqD;QACrD,yDAAyD;QACzD,IAAI,KAAK,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;YAC7B,SAAQ;QACV,CAAC;QAED,qEAAqE;QACrE,oMAAoM;QACpM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IACpE,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC", "sourcesContent": ["import { Filter } from \"builder-util/out/fs\"\nimport { Stats } from \"fs-extra\"\nimport { Minimatch } from \"minimatch\"\nimport * as path from \"path\"\nimport { NODE_MODULES_PATTERN } from \"../fileTransformer\"\n\n/** @internal */\nexport function hasMagic(pattern: Minimatch) {\n  const set = pattern.set\n  if (set.length > 1) {\n    return true\n  }\n\n  for (const i of set[0]) {\n    if (typeof i !== \"string\") {\n      return true\n    }\n  }\n\n  return false\n}\n\n// sometimes, destination may not contain path separator in the end (path to folder), but the src does. So let's ensure paths have path separators in the end\nfunction ensureEndSlash(s: string) {\n  return s.length === 0 || s.endsWith(path.sep) ? s : s + path.sep\n}\n\nfunction getRelativePath(file: string, srcWithEndSlash: string) {\n  if (!file.startsWith(srcWithEndSlash)) {\n    const index = file.indexOf(NODE_MODULES_PATTERN)\n    if (index < 0) {\n      throw new Error(`${file} must be under ${srcWithEndSlash}`)\n    } else {\n      return file.substring(index + 1 /* leading slash */)\n    }\n  }\n\n  let relative = file.substring(srcWithEndSlash.length)\n  if (path.sep === \"\\\\\") {\n    if (relative.startsWith(\"\\\\\")) {\n      // windows problem: double backslash, the above substring call removes root path with a single slash, so here can me some leftovers\n      relative = relative.substring(1)\n    }\n    relative = relative.replace(/\\\\/g, \"/\")\n  }\n  return relative\n}\n\n/** @internal */\nexport function createFilter(src: string, patterns: Array<Minimatch>, excludePatterns?: Array<Minimatch> | null): Filter {\n  const srcWithEndSlash = ensureEndSlash(src)\n  return (file, stat) => {\n    if (src === file) {\n      return true\n    }\n\n    const relative = getRelativePath(file, srcWithEndSlash)\n    // https://github.com/electron-userland/electron-builder/issues/867\n    return minimatchAll(relative, patterns, stat) && (excludePatterns == null || stat.isDirectory() || !minimatchAll(relative, excludePatterns, stat))\n  }\n}\n\n// https://github.com/joshwnj/minimatch-all/blob/master/index.js\nfunction minimatchAll(path: string, patterns: Array<Minimatch>, stat: Stats): boolean {\n  let match = false\n  for (const pattern of patterns) {\n    // If we've got a match, only re-test for exclusions.\n    // if we don't have a match, only re-test for inclusions.\n    if (match !== pattern.negate) {\n      continue\n    }\n\n    // partial match — pattern: foo/bar.txt path: foo — we must allow foo\n    // use it only for non-negate patterns: const m = new Minimatch(\"!node_modules/@(electron-download|electron)/**/*\", {dot: true }); m.match(\"node_modules\", true) will return false, but must be true\n    match = pattern.match(path, stat.isDirectory() && !pattern.negate)\n  }\n  return match\n}\n"]}