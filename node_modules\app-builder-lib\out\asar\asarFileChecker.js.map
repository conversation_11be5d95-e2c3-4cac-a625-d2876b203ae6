{"version": 3, "file": "asarFileChecker.js", "sourceRoot": "", "sources": ["../../src/asar/asarFileChecker.ts"], "names": [], "mappings": ";;;AAAA,4CAAgD;AAChD,iCAAuC;AAEvC,gBAAgB;AACT,KAAK,UAAU,kBAAkB,CAAC,QAAgB,EAAE,YAAoB,EAAE,aAAqB;IACpG,SAAS,KAAK,CAAC,IAAY;QACzB,OAAO,IAAI,KAAK,CAAC,GAAG,aAAa,KAAK,YAAY,aAAa,QAAQ,KAAK,IAAI,EAAE,CAAC,CAAA;IACrF,CAAC;IAED,IAAI,EAAE,CAAA;IACN,IAAI,CAAC;QACH,EAAE,GAAG,MAAM,IAAA,eAAQ,EAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,MAAM,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAA;IACnC,CAAC;IAED,IAAI,IAAiB,CAAA;IACrB,IAAI,CAAC;QACH,IAAI,GAAG,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;IACjC,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,MAAM,QAAQ,GAAG,MAAM,IAAA,eAAU,EAAC,QAAQ,CAAC,CAAA;QAC3C,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YACrB,MAAM,KAAK,CAAC,mDAAmD,CAAC,CAAA;QAClE,CAAC;QAED,8DAA8D;QAC9D,IAAI,GAAG,IAAI,CAAA;IACb,CAAC;IAED,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,MAAM,KAAK,CAAC,mDAAmD,CAAC,CAAA;IAClE,CAAC;IACD,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;QACpB,MAAM,KAAK,CAAC,sBAAsB,CAAC,CAAA;IACrC,CAAC;AACH,CAAC;AA/BD,gDA+BC", "sourcesContent": ["import { statOrNull } from \"builder-util/out/fs\"\nimport { Node, readAsar } from \"./asar\"\n\n/** @internal */\nexport async function checkFileInArchive(asarFile: string, relativeFile: string, messagePrefix: string) {\n  function error(text: string) {\n    return new Error(`${messagePrefix} \"${relativeFile}\" in the \"${asarFile}\" ${text}`)\n  }\n\n  let fs\n  try {\n    fs = await readAsar(asarFile)\n  } catch (e: any) {\n    throw error(`is corrupted: ${e}`)\n  }\n\n  let stat: Node | null\n  try {\n    stat = fs.getFile(relativeFile)\n  } catch (e: any) {\n    const fileStat = await statOrNull(asarFile)\n    if (fileStat == null) {\n      throw error(`does not exist. Seems like a wrong configuration.`)\n    }\n\n    // asar throws error on access to undefined object (info.link)\n    stat = null\n  }\n\n  if (stat == null) {\n    throw error(`does not exist. Seems like a wrong configuration.`)\n  }\n  if (stat.size === 0) {\n    throw error(`is corrupted: size 0`)\n  }\n}\n"]}