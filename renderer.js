const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Global variables
let cameraSettings = {
  ip: '************',
  port: '8080',
  camera: 'front',
  flash: false,
  nightVision: false,
  nvGain: 1,
  nvExposure: 1
};

let isConnected = false;
let root = '';
let contextMenu = null;

// DOM elements
const elements = {
  circleContainer: null,
  settingsOverlay: null,
  videoFeed: null,
  connectionStatus: null,
  controlsOverlay: null,
  contextMenuEl: null
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
  initializeElements();
  await loadSettings();
  setupEventListeners();
  setupContextMenu();
  setupResizing();
  updateUI();
});

function initializeElements() {
  elements.circleContainer = document.getElementById('circle-container');
  elements.settingsOverlay = document.getElementById('settings-overlay');
  elements.videoFeed = document.getElementById('video-feed');
  elements.connectionStatus = document.getElementById('connection-status');
  elements.controlsOverlay = document.getElementById('controls-overlay');
  elements.contextMenuEl = document.getElementById('context-menu');
}

async function loadSettings() {
  try {
    const stored = await ipcRenderer.invoke('get-stored-settings');
    cameraSettings = { ...cameraSettings, ...stored };
    updateSettingsUI();
  } catch (error) {
    console.error('Failed to load settings:', error);
  }
}

async function saveSettings() {
  try {
    await ipcRenderer.invoke('save-settings', cameraSettings);
  } catch (error) {
    console.error('Failed to save settings:', error);
  }
}

function updateSettingsUI() {
  document.getElementById('ip-addr').value = cameraSettings.ip;
  document.getElementById('port').value = cameraSettings.port;

  // Update radio buttons
  document.querySelector(`input[name="camera"][value="${cameraSettings.camera}"]`).checked = true;
  document.querySelector(`input[name="flash"][value="${cameraSettings.flash ? 'on' : 'off'}"]`).checked = true;
  document.querySelector(`input[name="nightvision"][value="${cameraSettings.nightVision ? 'on' : 'off'}"]`).checked = true;

  // Update sliders
  document.getElementById('nv-gain').value = cameraSettings.nvGain;
  document.getElementById('nv-exposure').value = cameraSettings.nvExposure;
  document.getElementById('nv-gain-value').textContent = cameraSettings.nvGain;
  document.getElementById('nv-exposure-value').textContent = cameraSettings.nvExposure;
}

function setupEventListeners() {
  // Settings button
  document.getElementById('settings-btn').addEventListener('click', showSettings);
  document.getElementById('close-settings').addEventListener('click', hideSettings);

  // Window controls
  document.getElementById('minimize-btn').addEventListener('click', () => {
    ipcRenderer.invoke('minimize-window');
  });

  document.getElementById('close-btn').addEventListener('click', () => {
    ipcRenderer.invoke('close-window');
  });

  // Connect button
  document.getElementById('connect-btn').addEventListener('click', connectToCamera);

  // Camera controls
  document.querySelectorAll('input[name="camera"]').forEach(radio => {
    radio.addEventListener('change', (e) => {
      cameraSettings.camera = e.target.value;
      saveSettings();
      if (isConnected) updateCameraSetting('ffc', e.target.value === 'front' ? 'on' : 'off');
    });
  });

  document.querySelectorAll('input[name="flash"]').forEach(radio => {
    radio.addEventListener('change', (e) => {
      cameraSettings.flash = e.target.value === 'on';
      saveSettings();
      if (isConnected) updateFlashSetting(cameraSettings.flash);
    });
  });

  document.querySelectorAll('input[name="nightvision"]').forEach(radio => {
    radio.addEventListener('change', (e) => {
      cameraSettings.nightVision = e.target.value === 'on';
      saveSettings();
      if (isConnected) updateCameraSetting('night_vision', e.target.value);
    });
  });

  // Night vision sliders
  document.getElementById('nv-gain').addEventListener('input', (e) => {
    cameraSettings.nvGain = parseInt(e.target.value);
    document.getElementById('nv-gain-value').textContent = cameraSettings.nvGain;
    saveSettings();
    if (isConnected) updateCameraSetting('night_vision_gain', cameraSettings.nvGain);
  });

  document.getElementById('nv-exposure').addEventListener('input', (e) => {
    cameraSettings.nvExposure = parseInt(e.target.value);
    document.getElementById('nv-exposure-value').textContent = cameraSettings.nvExposure;
    saveSettings();
    if (isConnected) updateCameraSetting('night_vision_average', cameraSettings.nvExposure);
  });

  // Screenshot button
  document.getElementById('take-snapshot').addEventListener('click', takeSnapshot);

  // IP/Port inputs
  document.getElementById('ip-addr').addEventListener('change', (e) => {
    cameraSettings.ip = e.target.value;
    saveSettings();
  });

  document.getElementById('port').addEventListener('change', (e) => {
    cameraSettings.port = e.target.value;
    saveSettings();
  });

  // Show controls on hover
  elements.circleContainer.addEventListener('mouseenter', () => {
    elements.controlsOverlay.classList.remove('hidden');
  });

  elements.circleContainer.addEventListener('mouseleave', () => {
    elements.controlsOverlay.classList.add('hidden');
  });
}

function setupContextMenu() {
  // Right-click context menu
  elements.circleContainer.addEventListener('contextmenu', (e) => {
    e.preventDefault();
    showContextMenu(e.clientX, e.clientY);
  });

  // Context menu items
  document.getElementById('menu-settings').addEventListener('click', () => {
    hideContextMenu();
    showSettings();
  });

  document.getElementById('menu-snapshot').addEventListener('click', () => {
    hideContextMenu();
    takeSnapshot();
  });

  document.getElementById('menu-minimize').addEventListener('click', () => {
    hideContextMenu();
    ipcRenderer.invoke('minimize-window');
  });

  document.getElementById('menu-close').addEventListener('click', () => {
    hideContextMenu();
    ipcRenderer.invoke('close-window');
  });

  // Hide context menu on click outside
  document.addEventListener('click', hideContextMenu);
}

function showContextMenu(x, y) {
  elements.contextMenuEl.style.left = `${x}px`;
  elements.contextMenuEl.style.top = `${y}px`;
  elements.contextMenuEl.classList.remove('hidden');
}

function hideContextMenu() {
  elements.contextMenuEl.classList.add('hidden');
}

function showSettings() {
  elements.settingsOverlay.classList.remove('hidden');
}

function hideSettings() {
  elements.settingsOverlay.classList.add('hidden');
}

function connectToCamera() {
  cameraSettings.ip = document.getElementById('ip-addr').value;
  cameraSettings.port = document.getElementById('port').value;
  root = `http://${cameraSettings.ip}:${cameraSettings.port}`;

  // Update video feed source
  elements.videoFeed.src = `${root}/video`;

  // Test connection
  elements.videoFeed.onload = () => {
    isConnected = true;
    updateConnectionStatus('Connected', 'Live feed active');
    saveSettings();
    getCameraStatus();
  };

  elements.videoFeed.onerror = () => {
    isConnected = false;
    updateConnectionStatus('Connection Failed', 'Check IP and port');
  };
}

function updateConnectionStatus(status, subtitle) {
  const statusText = elements.connectionStatus.querySelector('.status-text');
  const statusSubtitle = elements.connectionStatus.querySelector('.status-subtitle');

  statusText.textContent = status;
  statusSubtitle.textContent = subtitle;

  if (isConnected) {
    elements.connectionStatus.style.display = 'none';
  } else {
    elements.connectionStatus.style.display = 'block';
  }
}

function updateUI() {
  updateConnectionStatus('Not Connected', 'Right-click to configure');
}

// Camera control functions (ported from dashboard.js)
async function getCameraStatus() {
  if (!isConnected) return;

  try {
    const response = await fetch(`${root}/status.json`);
    const data = await response.json();

    // Update UI based on camera status
    if (data.curvals) {
      // Front/rear camera
      cameraSettings.camera = data.curvals.ffc === 'on' ? 'front' : 'rear';

      // Flash
      cameraSettings.flash = data.curvals.torch === 'on';

      // Night vision
      cameraSettings.nightVision = data.curvals.night_vision === 'on';
      cameraSettings.nvGain = parseInt(data.curvals.night_vision_gain) || 1;
      cameraSettings.nvExposure = parseInt(data.curvals.night_vision_average) || 1;

      updateSettingsUI();
      saveSettings();
    }
  } catch (error) {
    console.error('Failed to get camera status:', error);
  }
}

async function updateCameraSetting(setting, value) {
  if (!isConnected) return;

  try {
    await fetch(`${root}/settings/${setting}?set=${value}`);
  } catch (error) {
    console.error(`Failed to update ${setting}:`, error);
  }
}

async function updateFlashSetting(enabled) {
  if (!isConnected) return;

  try {
    const endpoint = enabled ? 'enabletorch' : 'disabletorch';
    await fetch(`${root}/${endpoint}`);
  } catch (error) {
    console.error('Failed to update flash:', error);
  }
}

async function takeSnapshot() {
  if (!isConnected) {
    alert('Please connect to camera first');
    return;
  }

  try {
    const timestamp = Date.now();
    const snapshotUrl = `${root}/shot.jpg?${timestamp}`;

    // Create a temporary link to download the image
    const link = document.createElement('a');
    link.href = snapshotUrl;
    link.download = `camera_snapshot_${timestamp}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('Snapshot taken:', snapshotUrl);
  } catch (error) {
    console.error('Failed to take snapshot:', error);
    alert('Failed to take snapshot');
  }
}

// Resizing functionality
function setupResizing() {
  const resizeHandle = document.getElementById('resize-handle');
  let isResizing = false;
  let startX, startY, startWidth, startHeight;

  resizeHandle.addEventListener('mousedown', (e) => {
    isResizing = true;
    startX = e.clientX;
    startY = e.clientY;

    const rect = elements.circleContainer.getBoundingClientRect();
    startWidth = rect.width;
    startHeight = rect.height;

    e.preventDefault();
  });

  document.addEventListener('mousemove', (e) => {
    if (!isResizing) return;

    const deltaX = e.clientX - startX;
    const deltaY = e.clientY - startY;

    // Use the larger delta to maintain circular shape
    const delta = Math.max(deltaX, deltaY);
    const newSize = Math.max(200, Math.min(600, startWidth + delta));

    // Update window size through IPC
    ipcRenderer.send('resize-window', newSize, newSize);
  });

  document.addEventListener('mouseup', () => {
    isResizing = false;
  });
}

// IPC listeners
ipcRenderer.on('toggle-settings-mode', (event, enabled) => {
  if (enabled) {
    showSettings();
  } else {
    hideSettings();
  }
});

// Auto-reconnect functionality
setInterval(() => {
  if (isConnected && root) {
    // Check if video feed is still working
    const img = new Image();
    img.onload = () => {
      // Connection is good
    };
    img.onerror = () => {
      // Connection lost
      isConnected = false;
      updateConnectionStatus('Connection Lost', 'Attempting to reconnect...');

      // Try to reconnect
      setTimeout(() => {
        if (!isConnected) {
          connectToCamera();
        }
      }, 2000);
    };
    img.src = `${root}/video?${Date.now()}`;
  }
}, 10000); // Check every 10 seconds
