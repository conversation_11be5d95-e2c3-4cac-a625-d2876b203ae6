{"lastValidatedTimestamp": 1753399611038, "projects": {"/mnt/dev/sda1/Users/<USER>/Documents/GitHub/ip-cam-dashboard": {"name": "ip-cam-circle", "version": "1.0.0"}}, "pnpmfiles": [], "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["."]}, "filteredInstall": false}