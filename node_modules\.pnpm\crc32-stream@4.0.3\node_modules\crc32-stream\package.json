{"name": "crc32-stream", "version": "4.0.3", "description": "a streaming CRC32 checksumer", "homepage": "https://github.com/archiverjs/node-crc32-stream", "author": {"name": "<PERSON>", "url": "http://christalkington.com/"}, "repository": {"type": "git", "url": "https://github.com/archiverjs/node-crc32-stream.git"}, "bugs": {"url": "https://github.com/archiverjs/node-crc32-stream/issues"}, "license": "MIT", "main": "lib/index.js", "files": ["lib"], "engines": {"node": ">= 10"}, "scripts": {"test": "mocha --reporter dot"}, "dependencies": {"crc-32": "^1.2.0", "readable-stream": "^3.4.0"}, "devDependencies": {"chai": "4.3.8", "mocha": "9.2.2"}, "keywords": ["crc32-stream", "crc32", "stream", "checksum"], "publishConfig": {"registry": "https://registry.npmjs.org/"}}