{"version": 3, "file": "nsisOptions.js", "sourceRoot": "", "sources": ["../../../src/targets/nsis/nsisOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TargetSpecificOptions } from \"../../core\"\nimport { CommonWindowsInstallerConfiguration } from \"../..\"\n\ninterface CustomNsisBinary {\n  /**\n   * @default https://github.com/electron-userland/electron-builder-binaries/releases/download\n   */\n  readonly url: string | null\n\n  /**\n   * @default VKMiizYdmNdJOWpRGz4trl4lD++BvYP2irAXpMilheUP0pc93iKlWAoP843Vlraj8YG19CVn0j+dCo/hURz9+Q==\n   */\n  readonly checksum?: string | null\n\n  /**\n   * @default *******\n   */\n  readonly version?: string | null\n\n  /**\n   * Whether or not to enable NSIS logging for debugging.\n   * Note: Requires a debug-enabled NSIS build.\n   * electron-builder's included `makensis` does not natively support debug-enabled NSIS installers currently, you must supply your own via `customNsisBinary?: CustomNsisBinary`\n   * In your custom nsis scripts, you can leverage this functionality via `LogSet` and `LogText`\n   */\n  readonly debugLogging?: boolean | null\n}\nexport interface CommonNsisOptions {\n  /**\n   * Whether to create [Unicode installer](http://nsis.sourceforge.net/Docs/Chapter1.html#intro-unicode).\n   * @default true\n   */\n  readonly unicode?: boolean\n\n  /**\n   * See [GUID vs Application Name](../configuration/nsis#guid-vs-application-name).\n   */\n  readonly guid?: string | null\n\n  /**\n   * If `warningsAsErrors` is `true` (default): NSIS will treat warnings as errors. If `warningsAsErrors` is `false`: NSIS will allow warnings.\n   * @default true\n   */\n  readonly warningsAsErrors?: boolean\n\n  /**\n   * @private\n   * @default false\n   */\n  readonly useZip?: boolean\n\n  /**\n   * Allows you to provide your own `makensis`, such as one with support for debug logging via LogSet and LogText. (Logging also requires option `debugLogging = true`)\n   */\n  readonly customNsisBinary?: CustomNsisBinary | null\n}\n\nexport interface NsisOptions extends CommonNsisOptions, CommonWindowsInstallerConfiguration, TargetSpecificOptions {\n  /**\n   * Whether to create one-click installer or assisted.\n   * @default true\n   */\n  readonly oneClick?: boolean\n\n  /**\n   * Whether to show install mode installer page (choice per-machine or per-user) for assisted installer. Or whether installation always per all users (per-machine).\n   *\n   * If `oneClick` is `true` (default): Whether to install per all users (per-machine).\n   *\n   * If `oneClick` is `false` and `perMachine` is `true`: no install mode installer page, always install per-machine.\n   *\n   * If `oneClick` is `false` and `perMachine` is `false` (default): install mode installer page.\n   * @default false\n   */\n  readonly perMachine?: boolean\n\n  /**\n   * Whether to set per-machine or per-user installation as default selection on the install mode installer page.\n   *\n   * @default false\n   */\n  readonly selectPerMachineByDefault?: boolean\n\n  /**\n   * *assisted installer only.* Allow requesting for elevation. If false, user will have to restart installer with elevated permissions.\n   * @default true\n   */\n  readonly allowElevation?: boolean\n\n  /**\n   * *assisted installer only.* Whether to allow user to change installation directory.\n   * @default false\n   */\n  readonly allowToChangeInstallationDirectory?: boolean\n\n  /**\n   * *assisted installer only.* remove the default uninstall welcome page.\n   * @default false\n   */\n  readonly removeDefaultUninstallWelcomePage?: boolean\n\n  /**\n   * The path to installer icon, relative to the [build resources](/configuration/configuration#MetadataDirectories-buildResources) or to the project directory.\n   * Defaults to `build/installerIcon.ico` or application icon.\n   */\n  readonly installerIcon?: string | null\n  /**\n   * The path to uninstaller icon, relative to the [build resources](/configuration/configuration#MetadataDirectories-buildResources) or to the project directory.\n   * Defaults to `build/uninstallerIcon.ico` or application icon.\n   */\n  readonly uninstallerIcon?: string | null\n  /**\n   * *assisted installer only.* `MUI_HEADERIMAGE`, relative to the [build resources](/configuration/configuration#MetadataDirectories-buildResources) or to the project directory.\n   * @default build/installerHeader.bmp\n   */\n  readonly installerHeader?: string | null\n  /**\n   * *one-click installer only.* The path to header icon (above the progress bar), relative to the [build resources](/configuration/configuration#MetadataDirectories-buildResources) or to the project directory.\n   * Defaults to `build/installerHeaderIcon.ico` or application icon.\n   */\n  readonly installerHeaderIcon?: string | null\n  /**\n   * *assisted installer only.* `MUI_WELCOMEFINISHPAGE_BITMAP`, relative to the [build resources](/configuration/configuration#MetadataDirectories-buildResources) or to the project directory.\n   * Defaults to `build/installerSidebar.bmp` or `${NSISDIR}\\\\Contrib\\\\Graphics\\\\Wizard\\\\nsis3-metro.bmp`. Image size 164 × 314 pixels.\n   */\n  readonly installerSidebar?: string | null\n  /**\n   * *assisted installer only.* `MUI_UNWELCOMEFINISHPAGE_BITMAP`, relative to the [build resources](/configuration/configuration#MetadataDirectories-buildResources) or to the project directory.\n   * Defaults to `installerSidebar` option or `build/uninstallerSidebar.bmp` or `build/installerSidebar.bmp` or `${NSISDIR}\\\\Contrib\\\\Graphics\\\\Wizard\\\\nsis3-metro.bmp`\n   */\n  readonly uninstallerSidebar?: string | null\n  /**\n   * The uninstaller display name in the control panel.\n   * @default ${productName} ${version}\n   */\n  readonly uninstallDisplayName?: string\n\n  /**\n   * The path to NSIS include script to customize installer. Defaults to `build/installer.nsh`. See [Custom NSIS script](#custom-nsis-script).\n   */\n  readonly include?: string | null\n  /**\n   * The path to NSIS script to customize installer. Defaults to `build/installer.nsi`. See [Custom NSIS script](#custom-nsis-script).\n   */\n  readonly script?: string | null\n\n  /**\n   * The path to EULA license file. Defaults to `license.txt` or `eula.txt` (or uppercase variants). In addition to `txt`, `rtf` and `html` supported (don't forget to use `target=\"_blank\"` for links).\n   *\n   * Multiple license files in different languages are supported — use lang postfix (e.g. `_de`, `_ru`). For example, create files `license_de.txt` and `license_en.txt` in the build resources.\n   * If OS language is german, `license_de.txt` will be displayed. See map of [language code to name](https://github.com/meikidd/iso-639-1/blob/master/src/data.js).\n   *\n   * Appropriate license file will be selected by user OS language.\n   */\n  readonly license?: string | null\n\n  /**\n   * The [artifact file name template](/configuration/configuration#artifact-file-name-template). Defaults to `${productName} Setup ${version}.${ext}`.\n   */\n  readonly artifactName?: string | null\n\n  /**\n   * *one-click installer only.* Whether to delete app data on uninstall.\n   * @default false\n   */\n  readonly deleteAppDataOnUninstall?: boolean\n\n  /**\n   * @private\n   */\n  differentialPackage?: boolean\n\n  /**\n   * Whether to display a language selection dialog. Not recommended (by default will be detected using OS language).\n   * @default false\n   */\n  readonly displayLanguageSelector?: boolean\n  /**\n   * The installer languages (e.g. `en_US`, `de_DE`). Change only if you understand what do you do and for what.\n   */\n  readonly installerLanguages?: Array<string> | string | null\n  /**\n   * [LCID Dec](https://msdn.microsoft.com/en-au/goglobal/bb964664.aspx), defaults to `1033`(`English - United States`).\n   */\n  readonly language?: string | null\n  /**\n   * Whether to create multi-language installer. Defaults to `unicode` option value.\n   */\n  readonly multiLanguageInstaller?: boolean\n  /**\n   * Whether to pack the elevate executable (required for electron-updater if per-machine installer used or can be used in the future). Ignored if `perMachine` is set to `true`.\n   * @default true\n   */\n  readonly packElevateHelper?: boolean\n\n  /**\n   * The file extension of files that will be not compressed. Applicable only for `extraResources` and `extraFiles` files.\n   * @default [\".avi\", \".mov\", \".m4v\", \".mp4\", \".m4p\", \".qt\", \".mkv\", \".webm\", \".vmdk\"]\n   */\n  readonly preCompressedFileExtensions?: Array<string> | string | null\n}\n\n/**\n * Portable options.\n */\nexport interface PortableOptions extends TargetSpecificOptions, CommonNsisOptions {\n  /**\n   * The [requested execution level](http://nsis.sourceforge.net/Reference/RequestExecutionLevel) for Windows.\n   * @default user\n   */\n  readonly requestExecutionLevel?: \"user\" | \"highest\" | \"admin\"\n\n  /**\n   * The unpack directory for the portable app resources.\n   *\n   * If set to a string, it will be the name in [TEMP](https://www.askvg.com/where-does-windows-store-temporary-files-and-how-to-change-temp-folder-location/) directory\n   * If set explicitly to `false`, it will use the Windows temp directory ($PLUGINSDIR) that is unique to each launch of the portable application.\n   *\n   * Defaults to [uuid](https://github.com/segmentio/ksuid) of build (changed on each build of portable executable).\n   */\n  readonly unpackDirName?: string | boolean\n\n  /**\n   * The image to show while the portable executable is extracting. This image must be a bitmap (`.bmp`) image.\n   */\n  readonly splashImage?: string | null\n}\n\n/**\n * Web Installer options.\n */\nexport interface NsisWebOptions extends NsisOptions {\n  /**\n   * The application package download URL. Optional — by default computed using publish configuration.\n   *\n   * URL like `https://example.com/download/latest` allows web installer to be version independent (installer will download latest application package).\n   * Please note — it is [full URL](https://github.com/electron-userland/electron-builder/issues/1810#issuecomment-317650878).\n   *\n   * Custom `X-Arch` http header is set to `32` or `64`.\n   */\n  readonly appPackageUrl?: string | null\n\n  /**\n   * The [artifact file name template](/configuration/configuration#artifact-file-name-template). Defaults to `${productName} Web Setup ${version}.${ext}`.\n   */\n  readonly artifactName?: string | null\n}\n"]}