{"version": 3, "file": "asarUtil.js", "sourceRoot": "", "sources": ["../../src/asar/asarUtil.ts"], "names": [], "mappings": ";;;AAAA,+CAAoD;AACpD,4CAA2E;AAC3E,2BAAwE;AACxE,0CAAwD;AACxD,6BAA4B;AAI5B,yDAA2E;AAC3E,iCAA6C;AAC7C,2CAAwD;AACxD,qDAAqD;AAErD,8DAA8D;AAC9D,MAAM,MAAM,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAA;AAE5C,gBAAgB;AAChB,MAAa,YAAY;IAKvB,YACmB,GAAW,EACX,WAAmB,EACnB,OAAoB,EACpB,aAA4B;QAH5B,QAAG,GAAH,GAAG,CAAQ;QACX,gBAAW,GAAX,WAAW,CAAQ;QACnB,YAAO,GAAP,OAAO,CAAa;QACpB,kBAAa,GAAb,aAAa,CAAe;QAR9B,OAAE,GAAG,IAAI,qBAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAUhD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;QACjD,IAAI,CAAC,YAAY,GAAG,GAAG,IAAI,CAAC,OAAO,WAAW,CAAA;IAChD,CAAC;IAED,kGAAkG;IAClG,KAAK,CAAC,IAAI,CAAC,QAAgC,EAAE,QAA+B;QAC1E,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE,CAAC;YAClC,kHAAkH;YAClH,MAAM,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;QACxE,CAAC;QACD,MAAM,IAAA,gBAAK,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QAC5D,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAgC,CAAA;QACpE,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,oBAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;QAC9F,CAAC;QACD,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,oBAAoB,CAAC,CAAA;IAC1D,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,OAAwB,EAAE,QAAkB;QAC/E,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QACjC,2BAA2B;QAC3B,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAA;QACtC,MAAM,0BAA0B,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;QAErE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YACvC,MAAM,IAAA,mCAAkB,EAAC,OAAO,EAAE,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,0BAA0B,CAAC,CAAA;QAChG,CAAC;QAED,MAAM,2BAA2B,GAAG,IAAI,GAAG,CAAS,YAAY,CAAC,CAAA;QAEjE,MAAM,0BAA0B,GAAG,KAAK,EAAE,iBAAyB,EAAE,OAAa,EAAE,EAAE;YACpF,KAAK,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;gBAC/B,IAAI,iBAAiB,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG,IAAI,iBAAiB,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBACjI,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAA;oBACvB,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;oBACnC,qJAAqJ;oBACrJ,uIAAuI;oBACvI,MAAM,IAAA,gBAAK,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,iBAAiB,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;oBACjF,MAAK;gBACP,CAAC;YACH,CAAC;QACH,CAAC,CAAA;QAED,MAAM,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAA;QACjD,MAAM,WAAW,GAAG,IAAI,+BAAgB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAA;QACpE,MAAM,UAAU,GAAG,IAAI,eAAU,EAAE,CAAA;QAEnC,IAAI,cAAc,GAAgB,IAAI,CAAA;QACtC,IAAI,cAAc,GAAkB,IAAI,CAAA;QAExC,MAAM,oBAAoB,GAAG,IAAI,GAAG,EAAU,CAAA;QAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACrD,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;YAC7B,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAC/B,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,SAAQ;YACV,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,0BAA0B,EAAE,IAAA,kCAAkB,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAA;YAElG,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC1B,MAAM,CAAC,GAAG,IAAW,CAAA;gBACrB,IAAI,CAAC,EAAE,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,YAAY,CAAA;gBAC5D,CAAC,CAAC,aAAa,GAAG,aAAa,CAAA;gBAC/B,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;gBAC3B,SAAQ;YACV,CAAC;YAED,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;YAC5C,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;gBACvB,UAAU,GAAG,EAAE,CAAA;YACjB,CAAC;YAED,IAAI,cAAc,KAAK,UAAU,EAAE,CAAC;gBAClC,IAAI,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,MAAM,IAAI,KAAK,CAAC,kDAAkD,UAAU,EAAE,CAAC,CAAA;gBACjF,CAAC;gBAED,cAAc,GAAG,UAAU,CAAA;gBAC3B,cAAc,GAAG,IAAI,CAAC,EAAE,CAAC,eAAe,CAAC,UAAU,CAAC,CAAA;gBACpD,wBAAwB;gBACxB,IAAI,UAAU,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;oBAClD,IAAI,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;wBACjC,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAA;oBAChC,CAAC;yBAAM,CAAC;wBACN,MAAM,0BAA0B,CAAC,UAAU,EAAE,cAAc,CAAC,CAAA;oBAC9D,CAAC;gBACH,CAAC;YACH,CAAC;YAED,MAAM,OAAO,GAAG,cAAe,CAAA;YAC/B,MAAM,OAAO,GAAG,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;YAC9E,MAAM,UAAU,GAAG,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAA;YACrG,MAAM,SAAS,GAAG,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,IAAA,oBAAQ,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAA,4BAAgB,EAAC,OAAO,CAAC,CAAA;YAC1F,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;YAC9H,IAAI,UAAU,EAAE,CAAC;gBACf,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;oBACtE,2BAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;oBAC3C,MAAM,IAAA,gBAAK,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;gBAC5E,CAAC;gBAED,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,CAAC,CAAA;gBAChE,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,CAAA;gBAClF,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,sBAAiB,EAAE,CAAC;oBACjD,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;gBAChC,CAAC;gBAED,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;YAC7B,CAAC;QACH,CAAC;QAED,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAChC,CAAC;QAED,OAAO,oBAAoB,CAAA;IAC7B,CAAC;IAEO,aAAa,CAAC,QAAgC,EAAE,oBAAuD;QAC7G,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,YAAY,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;YACzC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAA;YACxD,MAAM,SAAS,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAA;YAEzC,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,EAAE,CAAA;YACvC,UAAU,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;YAExC,MAAM,OAAO,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAA;YACrC,MAAM,WAAW,GAAG,IAAA,sBAAiB,EAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACnD,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;YAC/B,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;YAChC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;YAE1B,IAAI,YAAY,GAAG,CAAC,CAAA;YAEpB,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;YAC7B,IAAI,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAA;YACnC,IAAI,gBAAgB,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAA;YACnD,IAAI,oBAAoB,GAAG,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAE,CAAA;YACjE,MAAM,CAAC,GAAG,CAAC,KAAa,EAAE,EAAE;gBAC1B,OAAO,IAAI,EAAE,CAAC;oBACZ,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;wBAC1B,IAAI,EAAE,YAAY,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;4BACtC,WAAW,CAAC,GAAG,EAAE,CAAA;4BACjB,OAAM;wBACR,CAAC;6BAAM,CAAC;4BACN,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAA;4BACpC,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAA;4BAC1C,gBAAgB,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,gBAAgB,CAAA;4BAC1D,oBAAoB,GAAG,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAE,CAAA;4BACxE,KAAK,GAAG,CAAC,CAAA;wBACX,CAAC;oBACH,CAAC;oBAED,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;wBACrC,MAAK;oBACP,CAAC;yBAAM,CAAC;wBACN,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAA;wBACvC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;4BAC1C,IAAA,YAAO,EAAE,IAAY,CAAC,kBAAkB,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAG,IAAY,CAAC,aAAa,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;4BACxH,OAAM;wBACR,CAAC;oBACH,CAAC;oBACD,KAAK,EAAE,CAAA;gBACT,CAAC;gBAED,MAAM,IAAI,GAAG,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;gBAC1E,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAA;gBACzB,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;oBACxC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;oBAC3C,OAAM;gBACR,CAAC;gBAED,4CAA4C;gBAC5C,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;gBAC/B,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;oBAChD,IAAA,mBAAQ,EAAC,IAAI,CAAC;yBACX,IAAI,CAAC,EAAE,CAAC,EAAE;wBACT,WAAW,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;oBAC3C,CAAC,CAAC;yBACD,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,MAAM,CAAC,oBAAoB,IAAI,KAAK,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;gBAC3E,CAAC;qBAAM,CAAC;oBACN,MAAM,UAAU,GAAG,IAAA,qBAAgB,EAAC,IAAI,CAAC,CAAA;oBACzC,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;oBAC9B,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;oBAC1C,UAAU,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,EAAE;wBACzB,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE;4BAC3B,GAAG,EAAE,KAAK;yBACX,CAAC,CAAA;oBACJ,CAAC,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC,CAAA;YAED,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAC1C,CAAC,CAAC,CAAA;IACJ,CAAC;CACF;AAhND,oCAgNC;AAED,KAAK,UAAU,KAAK,CAAC,SAAwB,EAAE,YAAoB,EAAE,GAAW;IAC9E,MAAM,aAAa,GAAG,CAAC,MAAM,IAAA,mBAAQ,EAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QAClF,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC7B,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAG,CAAA;QAC/B,CAAC;QACD,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAA;QAClB,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;YACpB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACtB,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC,CAAC,CAAA;IAEF,MAAM,QAAQ,GAAkB,EAAE,CAAA;IAClC,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;QACjC,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC3C,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;YAC3C,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,CAAA;QAC9C,CAAC;IACH,CAAC;IAED,MAAM,WAAW,GAAkB,EAAE,CAAA;IACrC,IAAI,OAAO,GAAG,CAAC,CAAA;IACf,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,CAAA;IAC9B,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;QAC5B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAC5D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACxB,CAAC;IACH,CAAC;IACD,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;QAC7B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACtB,OAAO,IAAI,CAAC,CAAA;QACd,CAAC;IACH,CAAC;IACD,kBAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,EAAE,gCAAgC,CAAC,CAAA;IAC3F,OAAO,WAAW,CAAA;AACpB,CAAC;AAED,SAAS,cAAc,CAAC,UAAsB,EAAE,IAAwC,EAAE,MAAc,EAAE,WAAmB,EAAE,KAAY;IACzI,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,KAAK,CAAC,CAAA;IACpD,CAAC;SAAM,CAAC;QACN,OAAO,IAAA,oBAAS,EAAC,WAAW,EAAE,IAAI,CAAC,CAAA;IACrC,CAAC;AACH,CAAC", "sourcesContent": ["import { AsyncTaskManager, log } from \"builder-util\"\nimport { File<PERSON><PERSON>ier, Filter, MAX_FILE_REQUESTS } from \"builder-util/out/fs\"\nimport { symlink, createReadStream, createWriteStream, Stats } from \"fs\"\nimport { writeFile, readFile, mkdir } from \"fs/promises\"\nimport * as path from \"path\"\nimport { AsarOptions } from \"../options/PlatformSpecificBuildOptions\"\nimport { Packager } from \"../packager\"\nimport { PlatformPackager } from \"../platformPackager\"\nimport { getDestinationPath, ResolvedFileSet } from \"../util/appFileCopier\"\nimport { AsarFilesystem, Node } from \"./asar\"\nimport { hashFile, hashFileContents } from \"./integrity\"\nimport { detectUnpackedDirs } from \"./unpackDetector\"\n\n// eslint-disable-next-line @typescript-eslint/no-var-requires\nconst pickle = require(\"chromium-pickle-js\")\n\n/** @internal */\nexport class AsarPackager {\n  private readonly fs = new AsarFilesystem(this.src)\n  private readonly outFile: string\n  private readonly unpackedDest: string\n\n  constructor(\n    private readonly src: string,\n    private readonly destination: string,\n    private readonly options: AsarOptions,\n    private readonly unpackPattern: Filter | null\n  ) {\n    this.outFile = path.join(destination, \"app.asar\")\n    this.unpackedDest = `${this.outFile}.unpacked`\n  }\n\n  // sort files to minimize file change (i.e. asar file is not changed dramatically on small change)\n  async pack(fileSets: Array<ResolvedFileSet>, packager: PlatformPackager<any>) {\n    if (this.options.ordering != null) {\n      // ordering doesn't support transformed files, but ordering is not used functionality - wait user report to fix it\n      await order(fileSets[0].files, this.options.ordering, fileSets[0].src)\n    }\n    await mkdir(path.dirname(this.outFile), { recursive: true })\n    const unpackedFileIndexMap = new Map<ResolvedFileSet, Set<number>>()\n    for (const fileSet of fileSets) {\n      unpackedFileIndexMap.set(fileSet, await this.createPackageFromFiles(fileSet, packager.info))\n    }\n    await this.writeAsarFile(fileSets, unpackedFileIndexMap)\n  }\n\n  private async createPackageFromFiles(fileSet: ResolvedFileSet, packager: Packager) {\n    const metadata = fileSet.metadata\n    // search auto unpacked dir\n    const unpackedDirs = new Set<string>()\n    const rootForAppFilesWithoutAsar = path.join(this.destination, \"app\")\n\n    if (this.options.smartUnpack !== false) {\n      await detectUnpackedDirs(fileSet, unpackedDirs, this.unpackedDest, rootForAppFilesWithoutAsar)\n    }\n\n    const dirToCreateForUnpackedFiles = new Set<string>(unpackedDirs)\n\n    const correctDirNodeUnpackedFlag = async (filePathInArchive: string, dirNode: Node) => {\n      for (const dir of unpackedDirs) {\n        if (filePathInArchive.length > dir.length + 2 && filePathInArchive[dir.length] === path.sep && filePathInArchive.startsWith(dir)) {\n          dirNode.unpacked = true\n          unpackedDirs.add(filePathInArchive)\n          // not all dirs marked as unpacked after first iteration - because node module dir can be marked as unpacked after processing node module dir content\n          // e.g. node-notifier/example/advanced.js processed, but only on process vendor/terminal-notifier.app module will be marked as unpacked\n          await mkdir(path.join(this.unpackedDest, filePathInArchive), { recursive: true })\n          break\n        }\n      }\n    }\n\n    const transformedFiles = fileSet.transformedFiles\n    const taskManager = new AsyncTaskManager(packager.cancellationToken)\n    const fileCopier = new FileCopier()\n\n    let currentDirNode: Node | null = null\n    let currentDirPath: string | null = null\n\n    const unpackedFileIndexSet = new Set<number>()\n\n    for (let i = 0, n = fileSet.files.length; i < n; i++) {\n      const file = fileSet.files[i]\n      const stat = metadata.get(file)\n      if (stat == null) {\n        continue\n      }\n\n      const pathInArchive = path.relative(rootForAppFilesWithoutAsar, getDestinationPath(file, fileSet))\n\n      if (stat.isSymbolicLink()) {\n        const s = stat as any\n        this.fs.getOrCreateNode(pathInArchive).link = s.relativeLink\n        s.pathInArchive = pathInArchive\n        unpackedFileIndexSet.add(i)\n        continue\n      }\n\n      let fileParent = path.dirname(pathInArchive)\n      if (fileParent === \".\") {\n        fileParent = \"\"\n      }\n\n      if (currentDirPath !== fileParent) {\n        if (fileParent.startsWith(\"..\")) {\n          throw new Error(`Internal error: path must not start with \"..\": ${fileParent}`)\n        }\n\n        currentDirPath = fileParent\n        currentDirNode = this.fs.getOrCreateNode(fileParent)\n        // do not check for root\n        if (fileParent !== \"\" && !currentDirNode.unpacked) {\n          if (unpackedDirs.has(fileParent)) {\n            currentDirNode.unpacked = true\n          } else {\n            await correctDirNodeUnpackedFlag(fileParent, currentDirNode)\n          }\n        }\n      }\n\n      const dirNode = currentDirNode!\n      const newData = transformedFiles == null ? undefined : transformedFiles.get(i)\n      const isUnpacked = dirNode.unpacked || (this.unpackPattern != null && this.unpackPattern(file, stat))\n      const integrity = newData === undefined ? await hashFile(file) : hashFileContents(newData)\n      this.fs.addFileNode(file, dirNode, newData == undefined ? stat.size : Buffer.byteLength(newData), isUnpacked, stat, integrity)\n      if (isUnpacked) {\n        if (!dirNode.unpacked && !dirToCreateForUnpackedFiles.has(fileParent)) {\n          dirToCreateForUnpackedFiles.add(fileParent)\n          await mkdir(path.join(this.unpackedDest, fileParent), { recursive: true })\n        }\n\n        const unpackedFile = path.join(this.unpackedDest, pathInArchive)\n        taskManager.addTask(copyFileOrData(fileCopier, newData, file, unpackedFile, stat))\n        if (taskManager.tasks.length > MAX_FILE_REQUESTS) {\n          await taskManager.awaitTasks()\n        }\n\n        unpackedFileIndexSet.add(i)\n      }\n    }\n\n    if (taskManager.tasks.length > 0) {\n      await taskManager.awaitTasks()\n    }\n\n    return unpackedFileIndexSet\n  }\n\n  private writeAsarFile(fileSets: Array<ResolvedFileSet>, unpackedFileIndexMap: Map<ResolvedFileSet, Set<number>>): Promise<any> {\n    return new Promise((resolve, reject) => {\n      const headerPickle = pickle.createEmpty()\n      headerPickle.writeString(JSON.stringify(this.fs.header))\n      const headerBuf = headerPickle.toBuffer()\n\n      const sizePickle = pickle.createEmpty()\n      sizePickle.writeUInt32(headerBuf.length)\n\n      const sizeBuf = sizePickle.toBuffer()\n      const writeStream = createWriteStream(this.outFile)\n      writeStream.on(\"error\", reject)\n      writeStream.on(\"close\", resolve)\n      writeStream.write(sizeBuf)\n\n      let fileSetIndex = 0\n\n      let files = fileSets[0].files\n      let metadata = fileSets[0].metadata\n      let transformedFiles = fileSets[0].transformedFiles\n      let unpackedFileIndexSet = unpackedFileIndexMap.get(fileSets[0])!\n      const w = (index: number) => {\n        while (true) {\n          if (index >= files.length) {\n            if (++fileSetIndex >= fileSets.length) {\n              writeStream.end()\n              return\n            } else {\n              files = fileSets[fileSetIndex].files\n              metadata = fileSets[fileSetIndex].metadata\n              transformedFiles = fileSets[fileSetIndex].transformedFiles\n              unpackedFileIndexSet = unpackedFileIndexMap.get(fileSets[fileSetIndex])!\n              index = 0\n            }\n          }\n\n          if (!unpackedFileIndexSet.has(index)) {\n            break\n          } else {\n            const stat = metadata.get(files[index])\n            if (stat != null && stat.isSymbolicLink()) {\n              symlink((stat as any).linkRelativeToFile, path.join(this.unpackedDest, (stat as any).pathInArchive), () => w(index + 1))\n              return\n            }\n          }\n          index++\n        }\n\n        const data = transformedFiles == null ? null : transformedFiles.get(index)\n        const file = files[index]\n        if (data !== null && data !== undefined) {\n          writeStream.write(data, () => w(index + 1))\n          return\n        }\n\n        // https://github.com/yarnpkg/yarn/pull/3539\n        const stat = metadata.get(file)\n        if (stat != null && stat.size < 2 * 1024 * 1024) {\n          readFile(file)\n            .then(it => {\n              writeStream.write(it, () => w(index + 1))\n            })\n            .catch((e: any) => reject(`Cannot read file ${file}: ${e.stack || e}`))\n        } else {\n          const readStream = createReadStream(file)\n          readStream.on(\"error\", reject)\n          readStream.once(\"end\", () => w(index + 1))\n          readStream.on(\"open\", () => {\n            readStream.pipe(writeStream, {\n              end: false,\n            })\n          })\n        }\n      }\n\n      writeStream.write(headerBuf, () => w(0))\n    })\n  }\n}\n\nasync function order(filenames: Array<string>, orderingFile: string, src: string) {\n  const orderingFiles = (await readFile(orderingFile, \"utf8\")).split(\"\\n\").map(line => {\n    if (line.indexOf(\":\") !== -1) {\n      line = line.split(\":\").pop()!\n    }\n    line = line.trim()\n    if (line[0] === \"/\") {\n      line = line.slice(1)\n    }\n    return line\n  })\n\n  const ordering: Array<string> = []\n  for (const file of orderingFiles) {\n    const pathComponents = file.split(path.sep)\n    for (const pathComponent of pathComponents) {\n      ordering.push(path.join(src, pathComponent))\n    }\n  }\n\n  const sortedFiles: Array<string> = []\n  let missing = 0\n  const total = filenames.length\n  for (const file of ordering) {\n    if (!sortedFiles.includes(file) && filenames.includes(file)) {\n      sortedFiles.push(file)\n    }\n  }\n  for (const file of filenames) {\n    if (!sortedFiles.includes(file)) {\n      sortedFiles.push(file)\n      missing += 1\n    }\n  }\n  log.info({ coverage: ((total - missing) / total) * 100 }, \"ordering files in ASAR archive\")\n  return sortedFiles\n}\n\nfunction copyFileOrData(fileCopier: FileCopier, data: string | Buffer | undefined | null, source: string, destination: string, stats: Stats) {\n  if (data == null) {\n    return fileCopier.copy(source, destination, stats)\n  } else {\n    return writeFile(destination, data)\n  }\n}\n"]}