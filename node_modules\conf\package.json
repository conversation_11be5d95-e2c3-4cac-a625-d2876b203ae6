{"name": "conf", "version": "10.2.0", "description": "Simple config handling for your app or module", "license": "MIT", "repository": "sindresorhus/conf", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "main": "dist/source", "engines": {"node": ">=12"}, "scripts": {"test": "xo && npm run build && nyc ava", "build": "del-cli dist && tsc", "prepack": "npm run build"}, "files": ["dist/source"], "keywords": ["config", "store", "app", "storage", "conf", "configuration", "settings", "preferences", "json", "data", "persist", "persistent", "save", "load", "read", "write", "cache"], "dependencies": {"ajv": "^8.6.3", "ajv-formats": "^2.1.1", "atomically": "^1.7.0", "debounce-fn": "^4.0.0", "dot-prop": "^6.0.1", "env-paths": "^2.2.1", "json-schema-typed": "^7.0.3", "onetime": "^5.1.2", "pkg-up": "^3.1.0", "semver": "^7.3.5"}, "devDependencies": {"@ava/typescript": "^1.1.1", "@sindresorhus/tsconfig": "^0.7.0", "@types/node": "^14.14.41", "@types/semver": "^7.3.4", "@types/write-file-atomic": "^3.0.1", "ava": "^3.15.0", "clear-module": "^4.1.1", "del": "^6.0.0", "del-cli": "^3.0.1", "delay": "^5.0.0", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempy": "^1.0.1", "tsd": "^0.14.0", "typescript": "^4.4.3", "xo": "^0.38.2"}, "types": "dist/source", "ava": {"files": ["test/*", "!test/index.test-d.ts"], "timeout": "1m", "typescript": {"rewritePaths": {"test/": "dist/test/"}}}, "xo": {"rules": {"@typescript-eslint/no-implicit-any-catch": "off"}}, "nyc": {"extension": [".ts"], "exclude": ["**/test/**"]}}