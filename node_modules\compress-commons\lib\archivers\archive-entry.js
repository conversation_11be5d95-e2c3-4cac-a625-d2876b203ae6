/**
 * node-compress-commons
 *
 * Copyright (c) 2014 <PERSON>, contributors.
 * Licensed under the MIT license.
 * https://github.com/archiverjs/node-compress-commons/blob/master/LICENSE-MIT
 */
var ArchiveEntry = module.exports = function() {};

ArchiveEntry.prototype.getName = function() {};

ArchiveEntry.prototype.getSize = function() {};

ArchiveEntry.prototype.getLastModifiedDate = function() {};

ArchiveEntry.prototype.isDirectory = function() {};