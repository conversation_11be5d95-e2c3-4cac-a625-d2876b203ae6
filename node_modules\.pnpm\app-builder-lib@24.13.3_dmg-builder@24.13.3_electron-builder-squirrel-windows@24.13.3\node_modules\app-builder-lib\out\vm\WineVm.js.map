{"version": 3, "file": "WineVm.js", "sourceRoot": "", "sources": ["../../src/vm/WineVm.ts"], "names": [], "mappings": ";;;AAEA,kCAAkC;AAClC,6BAAgC;AAChC,6BAA4B;AAE5B,MAAa,aAAc,SAAQ,cAAS;IAC1C;QACE,KAAK,EAAE,CAAA;IACT,CAAC;IAED,6DAA6D;IAC7D,IAAI,CAAC,IAAY,EAAE,IAAmB,EAAE,OAAyB,EAAE,eAAe,GAAG,IAAI;QACvF,OAAO,IAAA,eAAQ,EAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IAC5C,CAAC;IAED,6DAA6D;IAC7D,KAAK,CAAC,IAAY,EAAE,IAAmB,EAAE,OAAsB,EAAE,YAAgC;QAC/F,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAA;IAChC,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACpC,CAAC;CACF;AAlBD,sCAkBC", "sourcesContent": ["import { SpawnOptions, ExecFileOptions } from \"child_process\"\nimport { ExtraSpawnOptions } from \"builder-util\"\nimport { execWine } from \"../wine\"\nimport { VmManager } from \"./vm\"\nimport * as path from \"path\"\n\nexport class WineVmManager extends VmManager {\n  constructor() {\n    super()\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  exec(file: string, args: Array<string>, options?: ExecFileOptions, isLogOutIfDebug = true): Promise<string> {\n    return execWine(file, null, args, options)\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  spawn(file: string, args: Array<string>, options?: SpawnOptions, extraOptions?: ExtraSpawnOptions): Promise<any> {\n    throw new Error(\"Unsupported\")\n  }\n\n  toVmFile(file: string): string {\n    return path.win32.join(\"Z:\", file)\n  }\n}\n"]}