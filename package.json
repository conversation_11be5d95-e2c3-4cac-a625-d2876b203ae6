{"name": "ip-cam-circle", "productName": "IP Camera Circle", "version": "1.0.0", "description": "Floating circular IP camera dashboard widget", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --publish=never"}, "dependencies": {"electron-context-menu": "^3.6.1", "electron-store": "^8.1.0"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "build": {"appId": "com.ipcam.circle", "productName": "IP Camera Circle", "directories": {"output": "dist"}, "files": ["main.js", "renderer.js", "circle.html", "styles.css", "package.json"], "win": {"target": "nsis", "icon": "icon.ico"}, "mac": {"target": "dmg", "icon": "icon.icns"}, "linux": {"target": "AppImage", "icon": "icon.png"}}}