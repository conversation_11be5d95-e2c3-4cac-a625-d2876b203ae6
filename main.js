const { app, BrowserWindow, Menu, ipc<PERSON>ain, shell, Tray, nativeImage } = require('electron');
const path = require('path');
const contextMenu = require('electron-context-menu');
const Store = require('electron-store');

// Initialize store for settings persistence
const store = new Store();

// Enable context menu
contextMenu({
  showInspectElement: false,
  showCopyImage: false,
  showSaveImage: false,
  showSaveImageAs: false,
  showServices: false
});

// Prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
}

let mainWindow;
let tray = null;
let isSettingsMode = false;

function createMainWindow() {
  // Get stored window position or use defaults
  const windowBounds = store.get('windowBounds', { x: 100, y: 100, width: 300, height: 300 });

  mainWindow = new BrowserWindow({
    x: windowBounds.x,
    y: windowBounds.y,
    width: windowBounds.width,
    height: windowBounds.height,
    minWidth: 200,
    minHeight: 200,
    maxWidth: 600,
    maxHeight: 600,
    frame: false,
    transparent: true,
    alwaysOnTop: true,
    resizable: true,
    movable: true,
    minimizable: true,
    maximizable: false,
    closable: true,
    focusable: true,
    skipTaskbar: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    }
  });

  mainWindow.loadFile('circle.html');

  // Save window position when moved or resized
  mainWindow.on('moved', () => {
    store.set('windowBounds', mainWindow.getBounds());
  });

  mainWindow.on('resized', () => {
    store.set('windowBounds', mainWindow.getBounds());
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle minimize to tray
  mainWindow.on('minimize', (event) => {
    if (tray) {
      event.preventDefault();
      mainWindow.hide();
    }
  });

  // Handle window ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  return mainWindow;
}

// App event handlers
app.whenReady().then(() => {
  createMainWindow();

  // Create application menu
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'Settings Mode',
          type: 'checkbox',
          checked: false,
          click: (menuItem) => {
            isSettingsMode = menuItem.checked;
            mainWindow.webContents.send('toggle-settings-mode', isSettingsMode);
          }
        },
        { type: 'separator' },
        {
          label: 'Always on Top',
          type: 'checkbox',
          checked: true,
          click: (menuItem) => {
            mainWindow.setAlwaysOnTop(menuItem.checked);
          }
        },
        { type: 'separator' },
        { role: 'quit' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createMainWindow();
  }
});

app.on('second-instance', () => {
  if (mainWindow) {
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  }
});

// IPC handlers
ipcMain.handle('get-stored-settings', () => {
  return store.get('cameraSettings', {
    ip: '************',
    port: '8080',
    camera: 'front',
    flash: false,
    nightVision: false,
    nvGain: 1,
    nvExposure: 1
  });
});

ipcMain.handle('save-settings', (event, settings) => {
  store.set('cameraSettings', settings);
  return true;
});

ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

ipcMain.on('resize-window', (event, width, height) => {
  if (mainWindow) {
    mainWindow.setSize(width, height);
  }
});