{"version": 3, "file": "wine.js", "sourceRoot": "", "sources": ["../src/wine.ts"], "names": [], "mappings": ";;;AACA,+CAAsD;AAEtD,eAAe;AACf,SAAgB,QAAQ,CAAC,IAAY,EAAE,SAAwB,IAAI,EAAE,UAAyB,EAAE,EAAE,UAA2B,EAAE;IAC7H,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YAC5B,YAAY;YACZ,OAAO,CAAC,OAAO,GAAG,GAAG,GAAG,IAAI,CAAA;QAC9B,CAAC;QACD,OAAO,IAAA,mBAAI,EAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;IACrC,CAAC;IAED,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;IAC5C,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;IACnC,CAAC;IACD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvB,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAA;IACrD,CAAC;IACD,OAAO,IAAA,gCAAiB,EAAC,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC,CAAA;AAC3D,CAAC;AAjBD,4BAiBC;AAED,eAAe;AACf,SAAgB,4BAA4B,CAAC,IAAmB,EAAE,OAAe;IAC/E,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;IACvB,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AALD,oEAKC", "sourcesContent": ["import { ExecFileOptions } from \"child_process\"\nimport { exec, executeAppBuilder } from \"builder-util\"\n\n/** @private */\nexport function execWine(file: string, file64: string | null = null, appArgs: Array<string> = [], options: ExecFileOptions = {}): Promise<string> {\n  if (process.platform === \"win32\") {\n    if (options.timeout == null) {\n      // 2 minutes\n      options.timeout = 120 * 1000\n    }\n    return exec(file, appArgs, options)\n  }\n\n  const commandArgs = [\"wine\", \"--ia32\", file]\n  if (file64 != null) {\n    commandArgs.push(\"--x64\", file64)\n  }\n  if (appArgs.length > 0) {\n    commandArgs.push(\"--args\", JSON.stringify(appArgs))\n  }\n  return executeAppBuilder(commandArgs, undefined, options)\n}\n\n/** @private */\nexport function prepareWindowsExecutableArgs(args: Array<string>, exePath: string) {\n  if (process.platform !== \"win32\") {\n    args.unshift(exePath)\n  }\n  return args\n}\n"]}