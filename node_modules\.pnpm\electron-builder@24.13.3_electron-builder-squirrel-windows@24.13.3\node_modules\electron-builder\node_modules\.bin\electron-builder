#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/dev/sda1/Users/<USER>/Documents/GitHub/ip-cam-dashboard/node_modules/.pnpm/electron-builder@24.13.3_electron-builder-squirrel-windows@24.13.3/node_modules/electron-builder/node_modules:/mnt/dev/sda1/Users/<USER>/Documents/GitHub/ip-cam-dashboard/node_modules/.pnpm/electron-builder@24.13.3_electron-builder-squirrel-windows@24.13.3/node_modules:/mnt/dev/sda1/Users/<USER>/Documents/GitHub/ip-cam-dashboard/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/dev/sda1/Users/<USER>/Documents/GitHub/ip-cam-dashboard/node_modules/.pnpm/electron-builder@24.13.3_electron-builder-squirrel-windows@24.13.3/node_modules/electron-builder/node_modules:/mnt/dev/sda1/Users/<USER>/Documents/GitHub/ip-cam-dashboard/node_modules/.pnpm/electron-builder@24.13.3_electron-builder-squirrel-windows@24.13.3/node_modules:/mnt/dev/sda1/Users/<USER>/Documents/GitHub/ip-cam-dashboard/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../cli.js" "$@"
else
  exec node  "$basedir/../../cli.js" "$@"
fi
