{"version": 3, "file": "builder.js", "sourceRoot": "", "sources": ["../src/builder.ts"], "names": [], "mappings": ";;;AAAA,+CAAyE;AACzE,+BAA8B;AAC9B,qDAAiH;AAEjH,+BAA8B;AAE9B,SAAgB,WAAW;IACzB,OAAO,KAAK,CAAC,mBAAmB,CAAC;QAC/B,sBAAsB,EAAE,KAAK;KAC9B,CAAC,CAAA;AACJ,CAAC;AAJD,kCAIC;AAcD,eAAe;AACf,SAAgB,gBAAgB,CAAC,IAAgB;IAC/C,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;QACzB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,GAAG,EAAsC,CAAA;IAE7D,SAAS,cAAc,CAAC,QAAkB,EAAE,KAAoB;QAC9D,SAAS,UAAU,CAAC,qBAA8B;YAChD,MAAM,MAAM,GAAG,KAAK,EAAQ,CAAA;YAC5B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;gBACb,MAAM,CAAC,IAAI,CAAC,mBAAI,CAAC,GAAG,CAAC,CAAA;YACvB,CAAC;YACD,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,MAAM,CAAC,IAAI,CAAC,mBAAI,CAAC,MAAM,CAAC,CAAA;YAC1B,CAAC;YACD,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,mBAAI,CAAC,KAAK,CAAC,CAAA;YACzB,CAAC;YACD,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM,CAAC,IAAI,CAAC,mBAAI,CAAC,IAAI,CAAC,CAAA;YACxB,CAAC;YACD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnB,MAAM,CAAC,IAAI,CAAC,mBAAI,CAAC,SAAS,CAAC,CAAA;YAC7B,CAAC;YAED,OAAO,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAA,6BAAc,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;QAC/F,CAAC;QAED,IAAI,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QACtC,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACvB,UAAU,GAAG,IAAI,GAAG,EAAuB,CAAA;YAC3C,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;QACnC,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,4BAAU,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YACvD,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC;gBACjD,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAA;YAC1C,CAAC;YACD,OAAM;QACR,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;YACvC,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;gBAClB,IAAA,uBAAQ,EAAC,UAAU,EAAE,IAAA,6BAAc,EAAC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAA;YACnG,CAAC;iBAAM,CAAC;gBACN,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpC,IAAA,uBAAQ,EAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;QACrB,cAAc,CAAC,0BAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;IACxC,CAAC;IAED,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;QACvB,cAAc,CAAC,0BAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IAC5C,CAAC;IAED,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;QACrB,cAAc,CAAC,0BAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAA;IAC5C,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;QACvB,cAAc,CAAC,0BAAQ,CAAC,OAAO,EAAE,EAAE,EAAE,CAAC,CAAA;IACxC,CAAC;IAED,MAAM,MAAM,GAAQ,EAAE,GAAG,IAAI,EAAE,CAAA;IAC/B,MAAM,CAAC,OAAO,GAAG,OAAO,CAAA;IAExB,OAAO,MAAM,CAAC,GAAG,CAAA;IACjB,OAAO,MAAM,CAAC,GAAG,CAAA;IACjB,OAAO,MAAM,CAAC,KAAK,CAAA;IACnB,OAAO,MAAM,CAAC,GAAG,CAAA;IAEjB,MAAM,CAAC,GAAG,MAAM,CAAA;IAChB,OAAO,CAAC,CAAC,CAAC,CAAA;IACV,OAAO,CAAC,CAAC,CAAC,CAAA;IACV,OAAO,CAAC,CAAC,CAAC,CAAA;IACV,OAAO,CAAC,CAAC,CAAC,CAAA;IACV,OAAO,CAAC,CAAC,OAAO,CAAA;IAChB,OAAO,CAAC,CAAC,KAAK,CAAA;IACd,OAAO,CAAC,CAAC,EAAE,CAAA;IACX,OAAO,CAAC,CAAC,CAAC,CAAA;IACV,OAAO,CAAC,CAAC,OAAO,CAAA;IAChB,OAAO,CAAC,CAAC,IAAI,CAAA;IACb,OAAO,CAAC,CAAC,CAAC,CAAA;IACV,OAAO,CAAC,CAAC,CAAC,CAAA;IACV,OAAO,CAAC,CAAC,EAAE,CAAA;IAEX,OAAO,MAAM,CAAC,IAAI,CAAA;IAClB,OAAO,MAAM,CAAC,GAAG,CAAA;IACjB,OAAO,MAAM,CAAC,MAAM,CAAA;IACpB,OAAO,MAAM,CAAC,KAAK,CAAA;IACnB,OAAO,MAAM,CAAC,SAAS,CAAA;IAEvB,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;IAE1B,8EAA8E;IAC9E,oEAAoE;IACpE,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAC1B,MAAM,SAAS,GAAkB,EAAE,CAAA;QACnC,KAAK,MAAM,UAAU,IAAI,MAAM,EAAE,CAAC;YAChC,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;gBACnC,IAAA,yBAAU,EAAC,SAAS,EAAE,UAAU,CAAC,CAAA;YACnC,CAAC;iBAAM,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;gBAC1C,SAAS,CAAC,OAAO,GAAG,UAAU,CAAA;YAChC,CAAC;QACH,CAAC;QAED,MAAM,GAAG,SAAS,CAAA;QAClB,MAAM,CAAC,MAAM,GAAG,SAAS,CAAA;IAC3B,CAAC;IAED,2GAA2G;IAC3G,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;QACjD,IAAI,MAAM,CAAC,aAAa,IAAI,IAAI,EAAE,CAAC;YACjC,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;QACnC,CAAC;QAED,0DAA0D;QAC1D,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,CAAA;QACrC,CAAC;QAED,kCAAkC;QAClC,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;YACxB,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QAC1B,CAAC;QACD,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YAC3B,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAC7B,CAAC;IACH,CAAC;IAED,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,CAAC,YAAY,IAAI,MAAM,CAAC,EAAE,CAAC;QAChD,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,OAAO,CAAA;IAC/B,CAAC;IACD,OAAO,CAAC,CAAC,OAAO,CAAA;IAEhB,OAAO,MAAsB,CAAA;AAC/B,CAAC;AAhJD,4CAgJC;AAED,SAAS,WAAW,CAAC,IAAS,EAAE,GAAW;IACzC,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA;IACvB,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;QACrB,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IAClB,CAAC;SAAM,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;IACnB,CAAC;SAAM,IAAI,KAAK,KAAK,MAAM,EAAE,CAAC;QAC5B,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;IAClB,CAAC;SAAM,IAAI,GAAG,KAAK,SAAS,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC1D,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAA;IAC9B,CAAC;SAAM,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QACtD,WAAW,CAAC,KAAK,CAAC,CAAA;IACpB,CAAC;AACH,CAAC;AAED,eAAe;AACf,SAAgB,WAAW,CAAC,IAAS;IACnC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;QACnD,WAAW,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;IACxB,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AALD,kCAKC;AAED,SAAgB,aAAa,CAAC,SAA0B,EAAE,IAAoB,EAAE,IAAoB;IAClG,MAAM,OAAO,GAAG,IAAI,GAAG,EAAsC,CAAA;IAC7D,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QACjC,MAAM,KAAK,GACT,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,0BAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,mBAAI,CAAC,GAAG,EAAE,mBAAI,CAAC,KAAK,EAAE,mBAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,mBAAI,CAAC,GAAG,EAAE,mBAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,6BAAc,EAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QACtK,MAAM,UAAU,GAAG,IAAI,GAAG,EAAuB,CAAA;QACjD,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;QAEjC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QAClD,CAAC;IACH,CAAC;IACD,OAAO,OAAO,CAAA;AAChB,CAAC;AAbD,sCAaC;AAED,SAAgB,KAAK,CAAC,UAAuB;IAC3C,MAAM,YAAY,GAAG,gBAAgB,CAAC,UAAU,IAAI,EAAE,CAAC,CAAA;IACvD,OAAO,IAAA,uBAAM,EAAC,YAAY,EAAE,IAAI,0BAAQ,CAAC,YAAY,CAAC,CAAC,CAAA;AACzD,CAAC;AAHD,sBAGC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,KAAiB;IACrD,MAAM,YAAY,GAAG,aAAa,CAAA;IAClC,MAAM,UAAU,GAAG,WAAW,CAAA;IAC9B,OAAO,KAAK;SACT,MAAM,CAAC,KAAK,EAAE;QACb,KAAK,EAAE,UAAU;QACjB,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC;QAC1B,WAAW,EAAE,6CAA6C,KAAK,CAAC,SAAS,CAAC,uBAAuB,CAAC,IAAI;QACtG,IAAI,EAAE,OAAO;KACd,CAAC;SACD,MAAM,CAAC,OAAO,EAAE;QACf,KAAK,EAAE,UAAU;QACjB,KAAK,EAAE,GAAG;QACV,WAAW,EAAE,6CAA6C,KAAK,CAAC,SAAS,CAAC,uBAAuB,CAAC,GAAG;QACrG,IAAI,EAAE,OAAO;KACd,CAAC;SACD,MAAM,CAAC,KAAK,EAAE;QACb,KAAK,EAAE,UAAU;QACjB,KAAK,EAAE,CAAC,GAAG,EAAE,SAAS,CAAC;QACvB,WAAW,EAAE,+CAA+C,KAAK,CAAC,SAAS,CAAC,uBAAuB,CAAC,GAAG;QACvG,IAAI,EAAE,OAAO;KACd,CAAC;SACD,MAAM,CAAC,KAAK,EAAE;QACb,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,eAAe;QAC5B,IAAI,EAAE,SAAS;KAChB,CAAC;SACD,MAAM,CAAC,MAAM,EAAE;QACd,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,gBAAgB;QAC7B,IAAI,EAAE,SAAS;KAChB,CAAC;SACD,MAAM,CAAC,QAAQ,EAAE;QAChB,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,kBAAkB;QAC/B,IAAI,EAAE,SAAS;KAChB,CAAC;SACD,MAAM,CAAC,OAAO,EAAE;QACf,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,iBAAiB;QAC9B,IAAI,EAAE,SAAS;KAChB,CAAC;SACD,MAAM,CAAC,WAAW,EAAE;QACnB,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,qBAAqB;QAClC,IAAI,EAAE,SAAS;KAChB,CAAC;SACD,MAAM,CAAC,KAAK,EAAE;QACb,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,qCAAqC;QAClD,IAAI,EAAE,SAAS;KAChB,CAAC;SACD,MAAM,CAAC,SAAS,EAAE;QACjB,KAAK,EAAE,YAAY;QACnB,KAAK,EAAE,GAAG;QACV,WAAW,EAAE,0BAA0B,KAAK,CAAC,SAAS,CAAC,uBAAuB,CAAC,EAAE;QACjF,OAAO,EAAE,CAAC,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAgB,CAAC;KACxE,CAAC;SACD,MAAM,CAAC,aAAa,EAAE;QACrB,KAAK,EAAE,CAAC,IAAI,CAAC;QACb,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,iEAAiE;KAC/E,CAAC;SACD,MAAM,CAAC,YAAY,EAAE;QACpB,KAAK,EAAE,CAAC,SAAS,CAAC;QAClB,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,uEAAuE;KACrF,CAAC;SACD,MAAM,CAAC,QAAQ,EAAE;QAChB,KAAK,EAAE,CAAC,GAAG,CAAC;QACZ,KAAK,EAAE,UAAU;QACjB,WAAW,EACT,4HAA4H,GAAG,KAAK,CAAC,SAAS,CAAC,uBAAuB,CAAC;KAC1K,CAAC;SACD,KAAK,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,QAAQ,CAAC;SACpC,OAAO,CAAC,uBAAuB,EAAE,oCAAoC,CAAC;SACtE,OAAO,CAAC,qCAAqC,EAAE,gCAAgC,CAAC;SAChF,OAAO,CAAC,+BAA+B,EAAE,wBAAwB,CAAC;SAClE,OAAO,CAAC,2CAA2C,EAAE,0CAA0C,CAAC;SAChG,OAAO,CAAC,8CAA8C,EAAE,oCAAoC,CAAC,CAAA;AAClG,CAAC;AAhFD,sDAgFC", "sourcesContent": ["import { addValue, Arch, archFromString, deepAssign } from \"builder-util\"\nimport * as chalk from \"chalk\"\nimport { build as _build, Configuration, DIR_TARGET, Packager, PackagerOptions, Platform } from \"app-builder-lib\"\nimport { PublishOptions } from \"electron-publish\"\nimport * as yargs from \"yargs\"\n\nexport function createYargs(): yargs.Argv<unknown> {\n  return yargs.parserConfiguration({\n    \"camel-case-expansion\": false,\n  })\n}\n\nexport interface BuildOptions extends PackagerOptions, PublishOptions {}\n\nexport interface CliOptions extends PackagerOptions, PublishOptions {\n  x64?: boolean\n  ia32?: boolean\n  armv7l?: boolean\n  arm64?: boolean\n  universal?: boolean\n\n  dir?: boolean\n}\n\n/** @private */\nexport function normalizeOptions(args: CliOptions): BuildOptions {\n  if (args.targets != null) {\n    return args\n  }\n\n  const targets = new Map<Platform, Map<Arch, Array<string>>>()\n\n  function processTargets(platform: Platform, types: Array<string>) {\n    function commonArch(currentIfNotSpecified: boolean): Array<Arch> {\n      const result = Array<Arch>()\n      if (args.x64) {\n        result.push(Arch.x64)\n      }\n      if (args.armv7l) {\n        result.push(Arch.armv7l)\n      }\n      if (args.arm64) {\n        result.push(Arch.arm64)\n      }\n      if (args.ia32) {\n        result.push(Arch.ia32)\n      }\n      if (args.universal) {\n        result.push(Arch.universal)\n      }\n\n      return result.length === 0 && currentIfNotSpecified ? [archFromString(process.arch)] : result\n    }\n\n    let archToType = targets.get(platform)\n    if (archToType == null) {\n      archToType = new Map<Arch, Array<string>>()\n      targets.set(platform, archToType)\n    }\n\n    if (types.length === 0) {\n      const defaultTargetValue = args.dir ? [DIR_TARGET] : []\n      for (const arch of commonArch(args.dir === true)) {\n        archToType.set(arch, defaultTargetValue)\n      }\n      return\n    }\n\n    for (const type of types) {\n      const suffixPos = type.lastIndexOf(\":\")\n      if (suffixPos > 0) {\n        addValue(archToType, archFromString(type.substring(suffixPos + 1)), type.substring(0, suffixPos))\n      } else {\n        for (const arch of commonArch(true)) {\n          addValue(archToType, arch, type)\n        }\n      }\n    }\n  }\n\n  if (args.mac != null) {\n    processTargets(Platform.MAC, args.mac)\n  }\n\n  if (args.linux != null) {\n    processTargets(Platform.LINUX, args.linux)\n  }\n\n  if (args.win != null) {\n    processTargets(Platform.WINDOWS, args.win)\n  }\n\n  if (targets.size === 0) {\n    processTargets(Platform.current(), [])\n  }\n\n  const result: any = { ...args }\n  result.targets = targets\n\n  delete result.dir\n  delete result.mac\n  delete result.linux\n  delete result.win\n\n  const r = result\n  delete r.m\n  delete r.o\n  delete r.l\n  delete r.w\n  delete r.windows\n  delete r.macos\n  delete r.$0\n  delete r._\n  delete r.version\n  delete r.help\n  delete r.c\n  delete r.p\n  delete r.pd\n\n  delete result.ia32\n  delete result.x64\n  delete result.armv7l\n  delete result.arm64\n  delete result.universal\n\n  let config = result.config\n\n  // config is array when combining dot-notation values with a config file value\n  // https://github.com/electron-userland/electron-builder/issues/2016\n  if (Array.isArray(config)) {\n    const newConfig: Configuration = {}\n    for (const configItem of config) {\n      if (typeof configItem === \"object\") {\n        deepAssign(newConfig, configItem)\n      } else if (typeof configItem === \"string\") {\n        newConfig.extends = configItem\n      }\n    }\n\n    config = newConfig\n    result.config = newConfig\n  }\n\n  // AJV cannot coerce \"null\" string to null if string is also allowed (because null string is a valid value)\n  if (config != null && typeof config !== \"string\") {\n    if (config.extraMetadata != null) {\n      coerceTypes(config.extraMetadata)\n    }\n\n    // ability to disable code sign using -c.mac.identity=null\n    if (config.mac != null) {\n      coerceValue(config.mac, \"identity\")\n    }\n\n    // fix Boolean type by coerceTypes\n    if (config.nsis != null) {\n      coerceTypes(config.nsis)\n    }\n    if (config.nsisWeb != null) {\n      coerceTypes(config.nsisWeb)\n    }\n  }\n\n  if (\"project\" in r && !(\"projectDir\" in result)) {\n    result.projectDir = r.project\n  }\n  delete r.project\n\n  return result as BuildOptions\n}\n\nfunction coerceValue(host: any, key: string): void {\n  const value = host[key]\n  if (value === \"true\") {\n    host[key] = true\n  } else if (value === \"false\") {\n    host[key] = false\n  } else if (value === \"null\") {\n    host[key] = null\n  } else if (key === \"version\" && typeof value === \"number\") {\n    host[key] = value.toString()\n  } else if (value != null && typeof value === \"object\") {\n    coerceTypes(value)\n  }\n}\n\n/** @private */\nexport function coerceTypes(host: any): any {\n  for (const key of Object.getOwnPropertyNames(host)) {\n    coerceValue(host, key)\n  }\n  return host\n}\n\nexport function createTargets(platforms: Array<Platform>, type?: string | null, arch?: string | null): Map<Platform, Map<Arch, Array<string>>> {\n  const targets = new Map<Platform, Map<Arch, Array<string>>>()\n  for (const platform of platforms) {\n    const archs =\n      arch === \"all\" ? (platform === Platform.MAC ? [Arch.x64, Arch.arm64, Arch.universal] : [Arch.x64, Arch.ia32]) : [archFromString(arch == null ? process.arch : arch)]\n    const archToType = new Map<Arch, Array<string>>()\n    targets.set(platform, archToType)\n\n    for (const arch of archs) {\n      archToType.set(arch, type == null ? [] : [type])\n    }\n  }\n  return targets\n}\n\nexport function build(rawOptions?: CliOptions): Promise<Array<string>> {\n  const buildOptions = normalizeOptions(rawOptions || {})\n  return _build(buildOptions, new Packager(buildOptions))\n}\n\n/**\n * @private\n */\nexport function configureBuildCommand(yargs: yargs.Argv): yargs.Argv {\n  const publishGroup = \"Publishing:\"\n  const buildGroup = \"Building:\"\n  return yargs\n    .option(\"mac\", {\n      group: buildGroup,\n      alias: [\"m\", \"o\", \"macos\"],\n      description: `Build for macOS, accepts target list (see ${chalk.underline(\"https://goo.gl/5uHuzj\")}).`,\n      type: \"array\",\n    })\n    .option(\"linux\", {\n      group: buildGroup,\n      alias: \"l\",\n      description: `Build for Linux, accepts target list (see ${chalk.underline(\"https://goo.gl/4vwQad\")})`,\n      type: \"array\",\n    })\n    .option(\"win\", {\n      group: buildGroup,\n      alias: [\"w\", \"windows\"],\n      description: `Build for Windows, accepts target list (see ${chalk.underline(\"https://goo.gl/jYsTEJ\")})`,\n      type: \"array\",\n    })\n    .option(\"x64\", {\n      group: buildGroup,\n      description: \"Build for x64\",\n      type: \"boolean\",\n    })\n    .option(\"ia32\", {\n      group: buildGroup,\n      description: \"Build for ia32\",\n      type: \"boolean\",\n    })\n    .option(\"armv7l\", {\n      group: buildGroup,\n      description: \"Build for armv7l\",\n      type: \"boolean\",\n    })\n    .option(\"arm64\", {\n      group: buildGroup,\n      description: \"Build for arm64\",\n      type: \"boolean\",\n    })\n    .option(\"universal\", {\n      group: buildGroup,\n      description: \"Build for universal\",\n      type: \"boolean\",\n    })\n    .option(\"dir\", {\n      group: buildGroup,\n      description: \"Build unpacked dir. Useful to test.\",\n      type: \"boolean\",\n    })\n    .option(\"publish\", {\n      group: publishGroup,\n      alias: \"p\",\n      description: `Publish artifacts, see ${chalk.underline(\"https://goo.gl/tSFycD\")}`,\n      choices: [\"onTag\", \"onTagOrDraft\", \"always\", \"never\", undefined as any],\n    })\n    .option(\"prepackaged\", {\n      alias: [\"pd\"],\n      group: buildGroup,\n      description: \"The path to prepackaged app (to pack in a distributable format)\",\n    })\n    .option(\"projectDir\", {\n      alias: [\"project\"],\n      group: buildGroup,\n      description: \"The path to project directory. Defaults to current working directory.\",\n    })\n    .option(\"config\", {\n      alias: [\"c\"],\n      group: buildGroup,\n      description:\n        \"The path to an electron-builder config. Defaults to `electron-builder.yml` (or `json`, or `json5`, or `js`, or `ts`), see \" + chalk.underline(\"https://goo.gl/YFRJOM\"),\n    })\n    .group([\"help\", \"version\"], \"Other:\")\n    .example(\"electron-builder -mwl\", \"build for macOS, Windows and Linux\")\n    .example(\"electron-builder --linux deb tar.xz\", \"build deb and tar.xz for Linux\")\n    .example(\"electron-builder --win --ia32\", \"build for Windows ia32\")\n    .example(\"electron-builder -c.extraMetadata.foo=bar\", \"set package.json property `foo` to `bar`\")\n    .example(\"electron-builder --config.nsis.unicode=false\", \"configure unicode options for NSIS\")\n}\n"]}