{"name": "electron-dl", "version": "3.5.2", "description": "Simplified file downloads for your Electron app", "license": "MIT", "repository": "sindresorhus/electron-dl", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "sideEffects": false, "engines": {"node": ">=12"}, "scripts": {"start": "electron run.js", "test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["electron", "app", "file", "download", "downloader", "progress"], "dependencies": {"ext-name": "^5.0.0", "pupa": "^2.0.1", "unused-filename": "^2.1.0"}, "devDependencies": {"@types/node": "^13.1.4", "ava": "^2.4.0", "cp-file": "^7.0.0", "electron": "^7.1.7", "minimist": "^1.2.0", "node-static": "^0.7.11", "pify": "^4.0.1", "spectron": "^9.0.0", "tsd": "^0.17.0", "typescript": "^4.4.3", "uuid": "^8.3.2", "xo": "^0.39.0"}, "xo": {"envs": ["node", "browser"]}}