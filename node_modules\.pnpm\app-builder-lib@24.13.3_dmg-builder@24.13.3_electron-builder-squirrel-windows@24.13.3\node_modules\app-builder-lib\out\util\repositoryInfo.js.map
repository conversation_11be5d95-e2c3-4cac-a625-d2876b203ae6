{"version": 3, "file": "repositoryInfo.js", "sourceRoot": "", "sources": ["../../src/util/repositoryInfo.ts"], "names": [], "mappings": ";;;AAAA,sDAA+D;AAC/D,uCAAmC;AACnC,qDAAkD;AAClD,6BAA4B;AAI5B,SAAgB,iBAAiB,CAAC,UAAkB,EAAE,QAAmB,EAAE,WAA6B;IACtG,OAAO,QAAQ,CAAC,UAAU,EAAE,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAA;AACvI,CAAC;AAFD,8CAEC;AAED,KAAK,UAAU,sBAAsB,CAAC,UAAkB;IACtD,MAAM,IAAI,GAAG,MAAM,IAAA,8BAAoB,EAAC,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC,CAAA;IAClG,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IAChC,MAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAA;IAC3C,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QACb,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;QACnB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YACzB,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;QACjB,CAAC;QAED,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YACxB,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAA;QACpC,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAED,KAAK,UAAU,QAAQ,CAAC,UAAkB,EAAE,IAAqC;IAC/E,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,OAAO,kBAAkB,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IACvE,CAAC;IAED,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAA;IAC3E,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAChC,OAAO;YACL,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;YACjB,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC;SACrB,CAAA;IACH,CAAC;IAED,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAA;IAChD,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAA;IACnD,IAAI,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpC,OAAO;YACL,IAAI;YACJ,OAAO;SACR,CAAA;IACH,CAAC;IAED,MAAM,GAAG,GAAG,MAAM,sBAAsB,CAAC,UAAU,CAAC,CAAA;IACpD,OAAO,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAA;AACrD,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW;IACrC,MAAM,IAAI,GAAQ,IAAA,yBAAO,EAAC,GAAG,CAAC,CAAA;IAC9B,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,OAAO,IAAI,CAAA;IACb,CAAC;IACD,OAAO,IAAI,CAAC,SAAS,CAAA;IACrB,OAAO,IAAI,CAAC,QAAQ,CAAA;IACpB,OAAO,IAAI,CAAC,YAAY,CAAA;IACxB,OAAO,IAAI,CAAC,YAAY,CAAA;IACxB,OAAO,IAAI,CAAC,WAAW,CAAA;IACvB,OAAO,IAAI,CAAC,eAAe,CAAA;IAC3B,OAAO,IAAI,CAAC,WAAW,CAAA;IACvB,OAAO,IAAI,CAAC,cAAc,CAAA;IAC1B,OAAO,IAAI,CAAC,cAAc,CAAA;IAC1B,OAAO,IAAI,CAAC,YAAY,CAAA;IACxB,OAAO,IAAI,CAAC,aAAa,CAAA;IACzB,OAAO,IAAI,CAAC,gBAAgB,CAAA;IAC5B,OAAO,IAAI,CAAC,YAAY,CAAA;IACxB,OAAO,IAAI,CAAC,SAAS,CAAA;IACrB,OAAO,IAAI,CAAC,YAAY,CAAA;IACxB,OAAO,IAAI,CAAC,UAAU,CAAA;IACtB,OAAO,IAAI,CAAC,OAAO,CAAA;IACnB,OAAO,IAAI,CAAC,IAAI,CAAA;IAChB,OAAO,IAAI,CAAC,kBAAkB,CAAA;IAC9B,OAAO,IAAI,CAAC,IAAI,CAAA;IAChB,OAAO,IAAI,CAAA;AACb,CAAC", "sourcesContent": ["import { orNullIfFileNotExist } from \"builder-util/out/promise\"\nimport { readFile } from \"fs-extra\"\nimport GitHost, { fromUrl } from \"hosted-git-info\"\nimport * as path from \"path\"\nimport { SourceRepositoryInfo } from \"../core\"\nimport { Metadata, RepositoryInfo } from \"../options/metadata\"\n\nexport function getRepositoryInfo(projectDir: string, metadata?: Metadata, devMetadata?: Metadata | null): Promise<SourceRepositoryInfo | null> {\n  return _getInfo(projectDir, (devMetadata == null ? null : devMetadata.repository) || (metadata == null ? null : metadata.repository))\n}\n\nasync function getGitUrlFromGitConfig(projectDir: string): Promise<string | null> {\n  const data = await orNullIfFileNotExist(readFile(path.join(projectDir, \".git\", \"config\"), \"utf8\"))\n  if (data == null) {\n    return null\n  }\n\n  const conf = data.split(/\\r?\\n/)\n  const i = conf.indexOf('[remote \"origin\"]')\n  if (i !== -1) {\n    let u = conf[i + 1]\n    if (!/^\\s*url =/.exec(u)) {\n      u = conf[i + 2]\n    }\n\n    if (/^\\s*url =/.exec(u)) {\n      return u.replace(/^\\s*url = /, \"\")\n    }\n  }\n  return null\n}\n\nasync function _getInfo(projectDir: string, repo?: RepositoryInfo | string | null): Promise<SourceRepositoryInfo | null> {\n  if (repo != null) {\n    return parseRepositoryUrl(typeof repo === \"string\" ? repo : repo.url)\n  }\n\n  const slug = process.env.TRAVIS_REPO_SLUG || process.env.APPVEYOR_REPO_NAME\n  if (slug != null) {\n    const splitted = slug.split(\"/\")\n    return {\n      user: splitted[0],\n      project: splitted[1],\n    }\n  }\n\n  const user = process.env.CIRCLE_PROJECT_USERNAME\n  const project = process.env.CIRCLE_PROJECT_REPONAME\n  if (user != null && project != null) {\n    return {\n      user,\n      project,\n    }\n  }\n\n  const url = await getGitUrlFromGitConfig(projectDir)\n  return url == null ? null : parseRepositoryUrl(url)\n}\n\nfunction parseRepositoryUrl(url: string): GitHost | null {\n  const info: any = fromUrl(url)\n  if (info == null) {\n    return null\n  }\n  delete info.protocols\n  delete info.treepath\n  delete info.filetemplate\n  delete info.bugstemplate\n  delete info.gittemplate\n  delete info.tarballtemplate\n  delete info.sshtemplate\n  delete info.sshurltemplate\n  delete info.browsetemplate\n  delete info.docstemplate\n  delete info.httpstemplate\n  delete info.shortcuttemplate\n  delete info.pathtemplate\n  delete info.pathmatch\n  delete info.protocols_re\n  delete info.committish\n  delete info.default\n  delete info.opts\n  delete info.browsefiletemplate\n  delete info.auth\n  return info\n}\n"]}